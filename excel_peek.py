import os
import sys
import struct

def read_excel_header(file_path):
    """
    尝试读取 Excel 文件的基本信息，不依赖外部库
    
    Args:
        file_path (str): Excel 文件路径
    
    Returns:
        dict: Excel 文件的基本信息
    """
    try:
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)
        
        # 读取文件前 1024 字节来尝试识别文件类型
        with open(file_path, 'rb') as f:
            header = f.read(1024)
        
        # 检查文件签名以确定是否为 Excel 文件
        is_xls = header[:8] == b'\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1'  # OLE2 格式 (.xls)
        is_xlsx = header[:4] == b'PK\x03\x04'  # ZIP 格式 (.xlsx)
        
        file_type = "未知"
        if is_xls:
            file_type = "Excel 97-2003 (.xls)"
        elif is_xlsx:
            file_type = "Excel 2007+ (.xlsx)"
        
        # 尝试估计行数（非常粗略）
        estimated_rows = file_size // 100  # 假设每行平均 100 字节
        
        # 创建基本信息摘要
        summary = {
            "文件名": file_name,
            "文件大小": f"{file_size / 1024 / 1024:.2f} MB",
            "文件类型": file_type,
            "估计行数": estimated_rows,
            "注意": "由于未安装 pandas 库，无法提供详细分析。请将文件导出为 CSV 格式以获取完整分析。"
        }
        
        return summary
    except Exception as e:
        print(f"读取 Excel 文件时出错: {str(e)}")
        return None

def print_excel_info(summary):
    """打印 Excel 文件的基本信息"""
    if not summary:
        return
    
    print(f"\n{'='*50}")
    print(f"Excel 文件基本信息: {summary['文件名']}")
    print(f"{'='*50}")
    
    for key, value in summary.items():
        if key != "文件名":
            print(f"{key}: {value}")
    
    print(f"\n{'='*50}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python excel_peek.py <Excel文件路径>")
        sys.exit(1)
        
    excel_file = sys.argv[1]
    
    if not os.path.exists(excel_file):
        print(f"错误: 文件 '{excel_file}' 不存在。")
        sys.exit(1)
        
    summary = read_excel_header(excel_file)
    
    if summary:
        print_excel_info(summary)
    else:
        print("无法读取 Excel 文件信息。")
