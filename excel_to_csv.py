import sys
import os
import subprocess

def convert_excel_to_csv(excel_file):
    """
    尝试将 Excel 文件转换为 CSV 格式
    
    Args:
        excel_file (str): Excel 文件路径
    
    Returns:
        str: CSV 文件路径，如果转换失败则返回 None
    """
    try:
        # 尝试导入 pandas
        import pandas as pd
        
        print(f"正在将 {excel_file} 转换为 CSV 格式...")
        
        # 读取 Excel 文件
        df = pd.read_excel(excel_file)
        
        # 创建 CSV 文件路径
        csv_file = os.path.splitext(excel_file)[0] + '.csv'
        
        # 保存为 CSV
        df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"转换完成，CSV 文件保存为: {csv_file}")
        return csv_file
        
    except ImportError:
        print("错误: 未安装 pandas 库，无法转换 Excel 文件。")
        return None
    except Exception as e:
        print(f"转换 Excel 文件时出错: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python excel_to_csv.py <Excel文件路径>")
        sys.exit(1)
        
    excel_file = sys.argv[1]
    
    if not os.path.exists(excel_file):
        print(f"错误: 文件 '{excel_file}' 不存在。")
        sys.exit(1)
        
    csv_file = convert_excel_to_csv(excel_file)
    
    if csv_file:
        # 尝试使用 excel_analyzer.py 分析 CSV 文件
        try:
            print("\n正在分析转换后的 CSV 文件...")
            subprocess.run(["python3", "excel_analyzer.py", csv_file])
        except Exception as e:
            print(f"分析 CSV 文件时出错: {str(e)}")
    else:
        print("转换失败，无法继续分析。")
