import os
import csv
import sys
from collections import Counter

def is_number(s):
    """检查字符串是否可以转换为数字"""
    try:
        float(s)
        return True
    except (ValueError, TypeError):
        return False

def analyze_csv_file(file_path, max_rows=1000):
    """
    分析 CSV 文件并提供摘要信息
    
    Args:
        file_path (str): CSV 文件路径
        max_rows (int): 要分析的最大行数
    """
    try:
        print(f"\n正在分析文件: {file_path}")
        
        # 读取 CSV 文件
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            reader = csv.reader(f)
            header = next(reader, [])
            
            # 只读取指定数量的行
            rows = []
            for i, row in enumerate(reader):
                if i >= max_rows:
                    break
                rows.append(row)
        
        # 基本信息
        total_rows = len(rows)
        total_columns = len(header)
        
        print(f"\n{'='*50}")
        print(f"文件摘要: {os.path.basename(file_path)}")
        print(f"{'='*50}")
        
        print(f"\n基本信息:")
        print(f"  总行数: {total_rows}+ (仅分析了前 {max_rows} 行)")
        print(f"  总列数: {total_columns}")
        print(f"  列名: {', '.join(header)}")
        
        # 分析每一列
        print(f"\n列统计信息:")
        for col_idx, col_name in enumerate(header):
            if col_idx >= len(header):
                continue
                
            # 获取列数据
            col_data = [row[col_idx] if col_idx < len(row) else "" for row in rows]
            
            # 计算空值
            empty_count = sum(1 for val in col_data if val.strip() == '')
            empty_percent = 0 if total_rows == 0 else empty_count/total_rows*100
            
            # 检查是否为数值列
            numeric_values = [float(val) for val in col_data if is_number(val)]
            is_numeric = len(numeric_values) > len(col_data) * 0.5 and len(numeric_values) > 0
            
            # 获取唯一值数量
            unique_values = len(set(col_data))
            
            # 获取最常见值
            counter = Counter(val for val in col_data if val.strip() != '')
            top_values = counter.most_common(5)
            
            print(f"\n  {col_name}:")
            print(f"    总值数: {len(col_data)}")
            print(f"    空值数: {empty_count} ({empty_percent:.1f}%)")
            print(f"    唯一值数: {unique_values}")
            print(f"    数据类型: {'数值' if is_numeric else '文本'}")
            
            if is_numeric and numeric_values:
                print(f"    最小值: {min(numeric_values)}")
                print(f"    最大值: {max(numeric_values)}")
                print(f"    平均值: {sum(numeric_values) / len(numeric_values):.2f}")
            
            print(f"    最常见值:")
            for val, count in top_values:
                print(f"      {val}: {count}")
        
        # 显示样本数据
        print(f"\n样本数据 (前5行):")
        for i, row in enumerate(rows[:5]):
            print(f"  第{i+1}行: {row}")
        
        print(f"\n{'='*50}")
        
        # 根据列名和数据推测文件用途
        purpose = guess_file_purpose(header, rows)
        print(f"\n文件可能用途: {purpose}")
        print(f"{'='*50}\n")
        
    except Exception as e:
        print(f"分析文件时出错: {str(e)}")

def guess_file_purpose(header, rows):
    """
    根据列名和数据推测文件用途
    
    Args:
        header (list): 列名列表
        rows (list): 数据行列表
    
    Returns:
        str: 推测的文件用途
    """
    header_lower = [h.lower() for h in header]
    
    # 检查是否包含银行相关关键词
    bank_keywords = ['银行', '支行', '分行', '联行号', '行号', 'bank', '金融机构', '机构']
    has_bank_keywords = any(keyword in ''.join(header_lower) for keyword in bank_keywords)
    
    # 检查是否包含联行号相关列
    has_bank_code = any('号' in h or 'code' in h.lower() or '代码' in h for h in header)
    
    # 检查是否包含地区相关列
    has_location = any('省' in h or '市' in h or '区' in h or '地区' in h or '地址' in h for h in header)
    
    if has_bank_keywords and has_bank_code:
        return """
这是一个银行支行联行号信息数据库，包含了银行支行的联行号和相关信息。

联行号（又称人行支付系统行号或超级网银行号）是由中国人民银行分配给各银行机构的唯一标识代码，用于银行间的转账结算和支付清算。

此文件的主要用途包括：
1. 银行间转账：当您需要跨行转账时，有些银行需要填写收款银行的联行号
2. 企业财务系统：企业在设置银行账户信息时可能需要填写联行号
3. 支付系统开发：开发支付相关系统时需要验证银行联行号
4. 银行信息查询：查询特定银行支行的详细信息和联行号

联行号通常是12位数字，前3位是银行代码，中间6位是地区代码，最后3位是支行代码。
"""
    
    elif has_bank_keywords:
        return "这似乎是一个银行相关的数据库，可能包含银行机构信息。"
    
    elif has_location and has_bank_code:
        return "这似乎是一个包含地区代码和机构代码的数据库，可能与金融机构或行政区划有关。"
    
    else:
        return "无法确定文件的具体用途，需要进一步分析文件内容。"

def main():
    dir_path = "/Users/<USER>/Downloads/hanghao"
    target_file = None
    
    # 查找目标文件
    for file in os.listdir(dir_path):
        if "支行联行号" in file and file.endswith(".csv"):
            target_file = os.path.join(dir_path, file)
            break
    
    if target_file:
        analyze_csv_file(target_file)
    else:
        print(f"在 {dir_path} 中未找到包含'支行联行号'的 CSV 文件")

if __name__ == "__main__":
    main()
