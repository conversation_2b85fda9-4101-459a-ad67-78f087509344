import sys
import csv

def read_csv_head(file_path, num_lines=10):
    """
    读取 CSV 文件的前几行
    
    Args:
        file_path (str): CSV 文件路径
        num_lines (int): 要读取的行数
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            reader = csv.reader(f)
            header = next(reader, [])
            
            print(f"\n文件: {file_path}")
            print(f"列名: {', '.join(header)}")
            print("\n前 {num_lines} 行数据:")
            
            for i, row in enumerate(reader):
                if i >= num_lines:
                    break
                print(f"行 {i+1}: {row}")
                
    except Exception as e:
        print(f"读取文件时出错: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python read_csv_head.py <CSV文件路径> [行数]")
        sys.exit(1)
        
    file_path = sys.argv[1]
    num_lines = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    
    read_csv_head(file_path, num_lines)
