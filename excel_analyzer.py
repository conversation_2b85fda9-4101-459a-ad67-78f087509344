import os
import argparse
import csv
import statistics
from collections import Counter

def is_number(s):
    """Check if a string can be converted to a number"""
    try:
        float(s)
        return True
    except (ValueError, TypeError):
        return False

def analyze_csv_file(file_path, start_row=0):
    """
    Analyze a CSV file starting from a specific row

    Args:
        file_path (str): Path to the CSV file
        start_row (int): Row number to start reading from (0-based)

    Returns:
        dict: Summary of the CSV file analysis
    """
    try:
        print(f"Analyzing {file_path}...")

        # Read CSV file
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            # Skip rows if needed
            for _ in range(start_row):
                next(f, None)

            # Read the CSV data
            reader = csv.reader(f)
            header = next(reader, [])
            rows = list(reader)

        # Prepare data structure for analysis
        columns = {i: [row[i] for row in rows if i < len(row)] for i in range(len(header))}
        column_names = {i: name for i, name in enumerate(header)}

        # Basic analysis
        total_rows = len(rows)
        total_columns = len(header)

        # Analyze each column
        column_stats = {}
        for i, col_name in column_names.items():
            col_data = columns.get(i, [])

            # Count empty values
            empty_count = sum(1 for val in col_data if val.strip() == '')

            # Check if column contains mostly numbers
            numeric_values = [float(val) for val in col_data if is_number(val)]
            is_numeric = len(numeric_values) > len(col_data) * 0.5

            # Calculate statistics
            stats = {
                "total_values": len(col_data),
                "empty_values": empty_count,
                "unique_values": len(set(col_data)),
                "is_numeric": is_numeric,
            }

            # Add numeric statistics if applicable
            if is_numeric and numeric_values:
                stats.update({
                    "min": min(numeric_values),
                    "max": max(numeric_values),
                    "mean": sum(numeric_values) / len(numeric_values),
                    "median": statistics.median(numeric_values) if len(numeric_values) > 0 else None,
                })

            # Get most common values
            counter = Counter(val for val in col_data if val.strip() != '')
            stats["top_values"] = dict(counter.most_common(5))

            column_stats[col_name] = stats

        # Create summary
        summary = {
            "file_name": os.path.basename(file_path),
            "total_rows": total_rows,
            "total_columns": total_columns,
            "column_names": list(column_names.values()),
            "column_stats": column_stats,
            "sample_data": [dict(zip(column_names.values(), row)) for row in rows[:5]]
        }

        return summary
    except Exception as e:
        print(f"Error analyzing {file_path}: {str(e)}")
        return None

def print_summary(summary):
    """Print a formatted summary of the CSV file analysis"""
    if not summary:
        return

    print(f"\n{'='*50}")
    print(f"SUMMARY FOR: {summary['file_name']}")
    print(f"{'='*50}")

    print(f"\nBASIC INFORMATION:")
    print(f"  Total rows: {summary['total_rows']}")
    print(f"  Total columns: {summary['total_columns']}")
    print(f"  Column names: {', '.join(summary['column_names'])}")

    print(f"\nCOLUMN STATISTICS:")
    for col_name, stats in summary['column_stats'].items():
        print(f"\n  {col_name}:")
        print(f"    Total values: {stats['total_values']}")
        print(f"    Empty values: {stats['empty_values']} ({stats['empty_values']/stats['total_values']*100:.1f}% if total > 0)")
        print(f"    Unique values: {stats['unique_values']}")
        print(f"    Data type: {'Numeric' if stats['is_numeric'] else 'Text'}")

        if stats['is_numeric']:
            print(f"    Min: {stats['min']}")
            print(f"    Max: {stats['max']}")
            print(f"    Mean: {stats['mean']:.2f}")
            print(f"    Median: {stats['median']}")

        print(f"    Top values:")
        for val, count in stats['top_values'].items():
            print(f"      {val}: {count}")

    print(f"\nSAMPLE DATA (First 5 rows):")
    for i, row in enumerate(summary['sample_data']):
        print(f"  Row {i+1}: {row}")

    print(f"\n{'='*50}\n")

def main():
    parser = argparse.ArgumentParser(description='Analyze CSV files with customizable starting row')
    parser.add_argument('file_path', nargs='?', help='Path to the CSV file')
    parser.add_argument('--start-row', type=int, default=0, help='Row number to start reading from (0-based)')
    parser.add_argument('--scan-dir', action='store_true', help='Scan current directory for CSV files')

    args = parser.parse_args()

    if args.scan_dir:
        # Get all CSV files in current directory
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]

        if not csv_files:
            print("No CSV files found in current directory.")
            return

        for file in csv_files:
            summary = analyze_csv_file(file, args.start_row)
            if summary:
                print_summary(summary)

    elif args.file_path:
        if not os.path.exists(args.file_path):
            print(f"Error: File '{args.file_path}' not found.")
            return

        summary = analyze_csv_file(args.file_path, args.start_row)
        if summary:
            print_summary(summary)

    else:
        print("Please provide a file path or use --scan-dir to scan the current directory.")
        parser.print_help()

if __name__ == "__main__":
    main()