import os
import pandas as pd
from collections import defaultdict

def analyze_excel_files():
    # Get all Excel files in current directory
    excel_files = [f for f in os.listdir('.') if f.endswith(('.xlsx', '.xls'))]
    
    if not excel_files:
        print("No Excel files found in current directory.")
        return
    
    summaries = {}
    
    for file in excel_files:
        print(f"Analyzing {file}...")
        try:
            # Read Excel file
            df = pd.read_excel(file)
            
            # Basic analysis
            summary = {
                "rows": len(df),
                "columns": len(df.columns),
                "column_names": list(df.columns),
                "missing_values": df.isna().sum().to_dict(),
                "numeric_columns": {col: {
                    "mean": df[col].mean(),
                    "min": df[col].min(),
                    "max": df[col].max()
                } for col in df.select_dtypes(include=['number']).columns}
            }
            
            summaries[file] = summary
            
        except Exception as e:
            print(f"Error analyzing {file}: {str(e)}")
    
    # Print summaries
    for file, summary in summaries.items():
        print(f"\n--- Summary for {file} ---")
        print(f"Rows: {summary['rows']}")
        print(f"Columns: {summary['columns']}")
        print(f"Column names: {', '.join(summary['column_names'])}")
        
        if summary['numeric_columns']:
            print("\nNumeric column statistics:")
            for col, stats in summary['numeric_columns'].items():
                print(f"  {col}: mean={stats['mean']:.2f}, min={stats['min']}, max={stats['max']}")

if __name__ == "__main__":
    analyze_excel_files()