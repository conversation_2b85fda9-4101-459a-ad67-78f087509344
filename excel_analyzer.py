import os
import argparse
import csv
import statistics
import sys
from collections import Counter

def is_number(s):
    """Check if a string can be converted to a number"""
    try:
        float(s)
        return True
    except (ValueError, TypeError):
        return False

def analyze_file(file_path, start_row=0):
    """
    Analyze a CSV or Excel file starting from a specific row

    Args:
        file_path (str): Path to the file
        start_row (int): Row number to start reading from (0-based)

    Returns:
        dict: Summary of the file analysis
    """
    try:
        print(f"分析文件 {file_path}...")

        # Check file extension
        _, ext = os.path.splitext(file_path)

        if ext.lower() in ['.xlsx', '.xls']:
            # Try to import pandas for Excel files
            try:
                import pandas as pd

                # Read Excel file
                df = pd.read_excel(file_path, skiprows=start_row)
                header = df.columns.tolist()
                rows = df.values.tolist()

                # Prepare data structure for analysis
                columns = {i: df.iloc[:, i].tolist() for i in range(len(header))}
                column_names = {i: name for i, name in enumerate(header)}

            except ImportError:
                print("错误: 无法分析 Excel 文件，因为未安装 pandas 和 openpyxl 库。")
                print("建议:")
                print("1. 请将 Excel 文件手动导出为 CSV 格式，然后再次运行此脚本")
                print("2. 或者安装所需库: pip install pandas openpyxl")
                return None

        elif ext.lower() == '.csv':
            # Read CSV file
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                # Skip rows if needed
                for _ in range(start_row):
                    next(f, None)

                # Read the CSV data
                reader = csv.reader(f)
                header = next(reader, [])
                rows = list(reader)

            # Prepare data structure for analysis
            columns = {i: [row[i] for row in rows if i < len(row)] for i in range(len(header))}
            column_names = {i: name for i, name in enumerate(header)}

        else:
            print(f"不支持的文件类型: {ext}")
            return None

        # Basic analysis
        total_rows = len(rows)
        total_columns = len(header)

        # Analyze each column
        column_stats = {}
        for i, col_name in column_names.items():
            col_data = columns.get(i, [])

            # Convert to string for consistent handling
            col_data = [str(val) if val is not None else '' for val in col_data]

            # Count empty values
            empty_count = sum(1 for val in col_data if val.strip() == '')

            # Check if column contains mostly numbers
            numeric_values = [float(val) for val in col_data if is_number(val)]
            is_numeric = len(numeric_values) > len(col_data) * 0.5 and len(numeric_values) > 0

            # Calculate statistics
            stats = {
                "total_values": len(col_data),
                "empty_values": empty_count,
                "unique_values": len(set(col_data)),
                "is_numeric": is_numeric,
            }

            # Add numeric statistics if applicable
            if is_numeric and numeric_values:
                stats.update({
                    "min": min(numeric_values),
                    "max": max(numeric_values),
                    "mean": sum(numeric_values) / len(numeric_values),
                    "median": statistics.median(numeric_values) if len(numeric_values) > 0 else None,
                })

            # Get most common values
            counter = Counter(val for val in col_data if val.strip() != '')
            stats["top_values"] = dict(counter.most_common(5))

            column_stats[col_name] = stats

        # Create summary
        summary = {
            "file_name": os.path.basename(file_path),
            "total_rows": total_rows,
            "total_columns": total_columns,
            "column_names": list(column_names.values()),
            "column_stats": column_stats,
            "sample_data": [dict(zip(column_names.values(), row)) for row in rows[:5] if row]
        }

        return summary
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {str(e)}")
        return None

def print_summary(summary):
    """打印文件分析摘要"""
    if not summary:
        return

    print(f"\n{'='*50}")
    print(f"文件摘要: {summary['file_name']}")
    print(f"{'='*50}")

    print(f"\n基本信息:")
    print(f"  总行数: {summary['total_rows']}")
    print(f"  总列数: {summary['total_columns']}")
    print(f"  列名: {', '.join(summary['column_names'])}")

    print(f"\n列统计信息:")
    for col_name, stats in summary['column_stats'].items():
        print(f"\n  {col_name}:")
        print(f"    总值数: {stats['total_values']}")
        empty_percent = 0 if stats['total_values'] == 0 else stats['empty_values']/stats['total_values']*100
        print(f"    空值数: {stats['empty_values']} ({empty_percent:.1f}%)")
        print(f"    唯一值数: {stats['unique_values']}")
        print(f"    数据类型: {'数值' if stats['is_numeric'] else '文本'}")

        if stats['is_numeric']:
            print(f"    最小值: {stats['min']}")
            print(f"    最大值: {stats['max']}")
            print(f"    平均值: {stats['mean']:.2f}")
            print(f"    中位数: {stats['median']}")

        print(f"    最常见值:")
        for val, count in stats['top_values'].items():
            print(f"      {val}: {count}")

    print(f"\n样本数据 (前5行):")
    for i, row in enumerate(summary['sample_data']):
        print(f"  第{i+1}行: {row}")

    print(f"\n{'='*50}\n")

def main():
    parser = argparse.ArgumentParser(description='分析 Excel 或 CSV 文件，支持自定义起始行')
    parser.add_argument('file_path', nargs='?', help='文件路径')
    parser.add_argument('--start-row', type=int, default=0, help='开始读取的行号（从0开始）')
    parser.add_argument('--scan-dir', action='store_true', help='扫描当前目录中的所有 Excel 和 CSV 文件')

    args = parser.parse_args()

    if args.scan_dir:
        # Get all Excel and CSV files in current directory
        files = [f for f in os.listdir('.') if f.endswith(('.xlsx', '.xls', '.csv'))]

        if not files:
            print("当前目录中没有找到 Excel 或 CSV 文件。")
            return

        for file in files:
            summary = analyze_file(file, args.start_row)
            if summary:
                print_summary(summary)

    elif args.file_path:
        if not os.path.exists(args.file_path):
            print(f"错误: 文件 '{args.file_path}' 不存在。")
            return

        summary = analyze_file(args.file_path, args.start_row)
        if summary:
            print_summary(summary)

    else:
        print("请提供文件路径或使用 --scan-dir 扫描当前目录。")
        parser.print_help()

if __name__ == "__main__":
    main()