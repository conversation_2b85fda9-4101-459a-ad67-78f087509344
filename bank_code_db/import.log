Starting data import...
MongoDB URI: mongodb://localhost:27017
Database: bank_code_db
CSV File: /Users/<USER>/Downloads/hanghao/bank_code_db/data/structured_bank_data_with_area.csv
Connected to MongoDB
Existing collections cleared
Processed 5000 rows, found 69 banks, 1531 areas, 5000 branches
Processed 10000 rows, found 70 banks, 2268 areas, 10000 branches
Processed 15000 rows, found 74 banks, 3106 areas, 15000 branches
Processed 20000 rows, found 76 banks, 3861 areas, 20000 branches
Processed 25000 rows, found 81 banks, 4674 areas, 25000 branches
Processed 30000 rows, found 84 banks, 5266 areas, 30000 branches
Processed 35000 rows, found 84 banks, 5794 areas, 35000 branches
Processed 40000 rows, found 84 banks, 6719 areas, 40000 branches
Processed 45000 rows, found 84 banks, 7458 areas, 45000 branches
Processed 50000 rows, found 84 banks, 8139 areas, 50000 branches
Processed 55000 rows, found 86 banks, 8884 areas, 55000 branches
Processed 60000 rows, found 86 banks, 9660 areas, 60000 branches
Processed 65000 rows, found 86 banks, 10186 areas, 65000 branches
Processed 70000 rows, found 86 banks, 10903 areas, 70000 branches
Processed 75000 rows, found 89 banks, 11656 areas, 75000 branches
Processed 80000 rows, found 112 banks, 12106 areas, 80000 branches
Processed 85000 rows, found 113 banks, 12446 areas, 85000 branches
Processed 90000 rows, found 113 banks, 13107 areas, 90000 branches
Processed 95000 rows, found 114 banks, 13623 areas, 95000 branches
Processed 100000 rows, found 114 banks, 14053 areas, 100000 branches
Processed 105000 rows, found 114 banks, 14489 areas, 105000 branches
Processed 110000 rows, found 114 banks, 15124 areas, 110000 branches
Processed 115000 rows, found 114 banks, 15718 areas, 115000 branches
Processed 120000 rows, found 114 banks, 16211 areas, 120000 branches
Processed 125000 rows, found 114 banks, 16545 areas, 125000 branches
Processed 130000 rows, found 117 banks, 16902 areas, 130000 branches
Processed 135000 rows, found 124 banks, 17720 areas, 135000 branches
Processed 140000 rows, found 126 banks, 17994 areas, 140000 branches
Processed 145000 rows, found 126 banks, 18232 areas, 145000 branches
Processed 150000 rows, found 126 banks, 18390 areas, 150000 branches
Processed 155000 rows, found 128 banks, 18622 areas, 155000 branches
Processed 160000 rows, found 132 banks, 18978 areas, 160000 branches
Processed 165000 rows, found 134 banks, 19906 areas, 165000 branches
Processed 170000 rows, found 135 banks, 21695 areas, 170000 branches
CSV processing completed. Processed 173355 rows.
Found 138 banks, 22806 areas, 173355 branches
Imported 138 banks
Imported 22806 areas
Imported 1000 of 173355 branches
Imported 2000 of 173355 branches
Imported 3000 of 173355 branches
Imported 4000 of 173355 branches
Imported 5000 of 173355 branches
Imported 6000 of 173355 branches
Imported 7000 of 173355 branches
Imported 8000 of 173355 branches
Imported 9000 of 173355 branches
Imported 10000 of 173355 branches
Imported 11000 of 173355 branches
Imported 12000 of 173355 branches
Imported 13000 of 173355 branches
Imported 14000 of 173355 branches
Imported 15000 of 173355 branches
Imported 16000 of 173355 branches
Imported 17000 of 173355 branches
Imported 18000 of 173355 branches
Imported 19000 of 173355 branches
Imported 20000 of 173355 branches
Imported 21000 of 173355 branches
Imported 22000 of 173355 branches
Imported 23000 of 173355 branches
Imported 24000 of 173355 branches
Imported 25000 of 173355 branches
Imported 26000 of 173355 branches
Imported 27000 of 173355 branches
Imported 28000 of 173355 branches
Imported 29000 of 173355 branches
Imported 30000 of 173355 branches
Imported 31000 of 173355 branches
Imported 32000 of 173355 branches
Imported 33000 of 173355 branches
Imported 34000 of 173355 branches
Imported 35000 of 173355 branches
Imported 36000 of 173355 branches
Imported 37000 of 173355 branches
Imported 38000 of 173355 branches
Imported 39000 of 173355 branches
Imported 40000 of 173355 branches
Imported 41000 of 173355 branches
Imported 42000 of 173355 branches
Imported 43000 of 173355 branches
Imported 44000 of 173355 branches
Imported 45000 of 173355 branches
Imported 46000 of 173355 branches
Imported 47000 of 173355 branches
Imported 48000 of 173355 branches
Imported 49000 of 173355 branches
Imported 50000 of 173355 branches
Imported 51000 of 173355 branches
Imported 52000 of 173355 branches
Imported 53000 of 173355 branches
Imported 54000 of 173355 branches
Imported 55000 of 173355 branches
Imported 56000 of 173355 branches
Imported 57000 of 173355 branches
Imported 58000 of 173355 branches
Imported 59000 of 173355 branches
Imported 60000 of 173355 branches
Imported 61000 of 173355 branches
Imported 62000 of 173355 branches
Imported 63000 of 173355 branches
Imported 64000 of 173355 branches
Imported 65000 of 173355 branches
Imported 66000 of 173355 branches
Imported 67000 of 173355 branches
Imported 68000 of 173355 branches
Imported 69000 of 173355 branches
Imported 70000 of 173355 branches
Imported 71000 of 173355 branches
Imported 72000 of 173355 branches
Imported 73000 of 173355 branches
Imported 74000 of 173355 branches
Imported 75000 of 173355 branches
Imported 76000 of 173355 branches
Imported 77000 of 173355 branches
Imported 78000 of 173355 branches
Imported 79000 of 173355 branches
Imported 80000 of 173355 branches
Imported 81000 of 173355 branches
Imported 82000 of 173355 branches
Imported 83000 of 173355 branches
Imported 84000 of 173355 branches
Imported 85000 of 173355 branches
Imported 86000 of 173355 branches
Imported 87000 of 173355 branches
Imported 88000 of 173355 branches
Imported 89000 of 173355 branches
Imported 90000 of 173355 branches
Imported 91000 of 173355 branches
Imported 92000 of 173355 branches
Imported 93000 of 173355 branches
Imported 94000 of 173355 branches
Imported 95000 of 173355 branches
Imported 96000 of 173355 branches
Imported 97000 of 173355 branches
Imported 98000 of 173355 branches
Imported 99000 of 173355 branches
Imported 100000 of 173355 branches
Imported 101000 of 173355 branches
Imported 102000 of 173355 branches
Imported 103000 of 173355 branches
Imported 104000 of 173355 branches
Imported 105000 of 173355 branches
Imported 106000 of 173355 branches
Imported 107000 of 173355 branches
Imported 108000 of 173355 branches
Imported 109000 of 173355 branches
Imported 110000 of 173355 branches
Imported 111000 of 173355 branches
Imported 112000 of 173355 branches
Imported 113000 of 173355 branches
Imported 114000 of 173355 branches
Imported 115000 of 173355 branches
Imported 116000 of 173355 branches
Imported 117000 of 173355 branches
Imported 118000 of 173355 branches
Imported 119000 of 173355 branches
Imported 120000 of 173355 branches
Imported 121000 of 173355 branches
Imported 122000 of 173355 branches
Imported 123000 of 173355 branches
Imported 124000 of 173355 branches
Imported 125000 of 173355 branches
Imported 126000 of 173355 branches
Imported 127000 of 173355 branches
Imported 128000 of 173355 branches
Imported 129000 of 173355 branches
Imported 130000 of 173355 branches
Imported 131000 of 173355 branches
Imported 132000 of 173355 branches
Imported 133000 of 173355 branches
Imported 134000 of 173355 branches
Imported 135000 of 173355 branches
Imported 136000 of 173355 branches
Imported 137000 of 173355 branches
Imported 138000 of 173355 branches
Imported 139000 of 173355 branches
Imported 140000 of 173355 branches
Imported 141000 of 173355 branches
Imported 142000 of 173355 branches
Imported 143000 of 173355 branches
Imported 144000 of 173355 branches
Imported 145000 of 173355 branches
Imported 146000 of 173355 branches
Imported 147000 of 173355 branches
Imported 148000 of 173355 branches
Imported 149000 of 173355 branches
Imported 150000 of 173355 branches
Imported 151000 of 173355 branches
Imported 152000 of 173355 branches
Imported 153000 of 173355 branches
Imported 154000 of 173355 branches
Imported 155000 of 173355 branches
Imported 156000 of 173355 branches
Imported 157000 of 173355 branches
Imported 158000 of 173355 branches
Imported 159000 of 173355 branches
Imported 160000 of 173355 branches
Imported 161000 of 173355 branches
Imported 162000 of 173355 branches
Imported 163000 of 173355 branches
Imported 164000 of 173355 branches
Imported 165000 of 173355 branches
Imported 166000 of 173355 branches
Imported 167000 of 173355 branches
Imported 168000 of 173355 branches
Imported 169000 of 173355 branches
Imported 170000 of 173355 branches
Imported 171000 of 173355 branches
Imported 172000 of 173355 branches
Imported 173000 of 173355 branches
Imported 173355 of 173355 branches
Creating indexes...
Error creating indexes: MongoServerError: Index already exists with a different name: bank_name_pinyin_idx
    at Connection.onMessage (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/connection.js:202:26)
    at MessageStream.<anonymous> (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/connection.js:61:60)
    at MessageStream.emit (node:events:518:28)
    at processIncomingData (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/message_stream.js:124:16)
    at MessageStream._write (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/message_stream.js:33:9)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Socket.ondata (node:internal/streams/readable:1009:22)
    at Socket.emit (node:events:518:28) {
  ok: 0,
  code: 85,
  codeName: 'IndexOptionsConflict',
  [Symbol(errorLabels)]: Set(0) {}
}
Error importing data: MongoServerError: Index already exists with a different name: bank_name_pinyin_idx
    at Connection.onMessage (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/connection.js:202:26)
    at MessageStream.<anonymous> (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/connection.js:61:60)
    at MessageStream.emit (node:events:518:28)
    at processIncomingData (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/message_stream.js:124:16)
    at MessageStream._write (/Users/<USER>/Downloads/hanghao/bank_code_db/node_modules/mongodb/lib/cmap/message_stream.js:33:9)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Socket.ondata (node:internal/streams/readable:1009:22)
    at Socket.emit (node:events:518:28) {
  ok: 0,
  code: 85,
  codeName: 'IndexOptionsConflict',
  [Symbol(errorLabels)]: Set(0) {}
}
MongoDB connection closed
