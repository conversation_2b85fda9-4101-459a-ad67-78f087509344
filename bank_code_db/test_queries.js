/**
 * 银行联行号查询系统 - 查询测试脚本
 */

const { MongoClient } = require('mongodb');

// 配置信息
const config = {
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
  dbName: 'bank_code_db'
};

async function testQueries() {
  console.log('🔍 测试银行联行号查询功能...');
  console.log('='.repeat(50));
  
  const client = new MongoClient(config.mongoUri);
  
  try {
    await client.connect();
    console.log('✅ 连接到 MongoDB');
    
    const db = client.db(config.dbName);
    
    // 测试1: 查询所有银行
    console.log('\n📋 测试1: 查询所有银行');
    const banks = await db.collection('banks').find({}).toArray();
    console.log(`找到 ${banks.length} 家银行:`);
    banks.forEach(bank => {
      console.log(`  - ${bank.bank_short_name} (${bank.bank_code})`);
    });
    
    // 测试2: 按银行名称查询支行
    console.log('\n🏦 测试2: 查询工商银行的所有支行');
    const icbcBranches = await db.collection('bank_branches')
      .find({ "bank.bank_short_name": "工商银行" })
      .toArray();
    console.log(`找到 ${icbcBranches.length} 个工商银行支行:`);
    icbcBranches.forEach(branch => {
      console.log(`  - ${branch.branch_name} (${branch.bank_no})`);
      console.log(`    地址: ${branch.address}`);
    });
    
    // 测试3: 按地区查询支行
    console.log('\n📍 测试3: 查询北京市的所有支行');
    const beijingBranches = await db.collection('bank_branches')
      .find({ "area.province": "北京市" })
      .toArray();
    console.log(`找到 ${beijingBranches.length} 个北京市支行:`);
    beijingBranches.forEach(branch => {
      console.log(`  - ${branch.bank.bank_short_name} ${branch.branch_name}`);
      console.log(`    联行号: ${branch.bank_no}`);
    });
    
    // 测试4: 按联行号精确查询
    console.log('\n🔢 测试4: 按联行号查询 (************)');
    const branchByNo = await db.collection('bank_branches')
      .findOne({ "bank_no": "************" });
    if (branchByNo) {
      console.log(`找到支行:`);
      console.log(`  - 银行: ${branchByNo.bank.bank_short_name}`);
      console.log(`  - 支行: ${branchByNo.branch_name}`);
      console.log(`  - 地址: ${branchByNo.address}`);
      console.log(`  - 电话: ${branchByNo.telephone}`);
    } else {
      console.log('未找到该联行号对应的支行');
    }
    
    // 测试5: 聚合查询 - 按省份统计支行数量
    console.log('\n📊 测试5: 按省份统计支行数量');
    const provinceStats = await db.collection('bank_branches')
      .aggregate([
        { $group: { _id: "$area.province", count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ])
      .toArray();
    console.log('各省份支行数量:');
    provinceStats.forEach(stat => {
      console.log(`  - ${stat._id}: ${stat.count} 个支行`);
    });
    
    // 测试6: 聚合查询 - 按银行统计支行数量
    console.log('\n🏛️ 测试6: 按银行统计支行数量');
    const bankStats = await db.collection('bank_branches')
      .aggregate([
        { $group: { _id: "$bank.bank_short_name", count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ])
      .toArray();
    console.log('各银行支行数量:');
    bankStats.forEach(stat => {
      console.log(`  - ${stat._id}: ${stat.count} 个支行`);
    });
    
    // 测试7: 复合查询 - 查询特定银行在特定地区的支行
    console.log('\n🎯 测试7: 查询工商银行在上海市的支行');
    const icbcShanghai = await db.collection('bank_branches')
      .find({ 
        "bank.bank_short_name": "工商银行",
        "area.province": "上海市"
      })
      .toArray();
    console.log(`找到 ${icbcShanghai.length} 个工商银行上海支行:`);
    icbcShanghai.forEach(branch => {
      console.log(`  - ${branch.branch_name} (${branch.bank_no})`);
      console.log(`    地址: ${branch.address}`);
    });
    
    // 测试8: 模糊查询 - 查询包含"营业部"的支行
    console.log('\n🔍 测试8: 查询包含"营业部"的支行');
    const yingyebuBranches = await db.collection('bank_branches')
      .find({ "branch_name": { $regex: "营业部", $options: "i" } })
      .limit(5)
      .toArray();
    console.log(`找到 ${yingyebuBranches.length} 个营业部支行 (显示前5个):`);
    yingyebuBranches.forEach(branch => {
      console.log(`  - ${branch.bank.bank_short_name} ${branch.branch_name}`);
      console.log(`    联行号: ${branch.bank_no}`);
    });
    
    // 测试9: 查询地区信息
    console.log('\n🗺️ 测试9: 查询地区信息');
    const areas = await db.collection('areas').find({}).toArray();
    console.log(`找到 ${areas.length} 个地区:`);
    areas.forEach(area => {
      console.log(`  - ${area.province} ${area.city || ''} ${area.district || ''} (${area.area_code})`);
    });
    
    // 测试10: 数据库版本信息
    console.log('\n📋 测试10: 查询数据库版本信息');
    const versions = await db.collection('db_versions')
      .find({})
      .sort({ created_at: -1 })
      .toArray();
    console.log('数据库版本历史:');
    versions.forEach(version => {
      console.log(`  - v${version.version}: ${version.description}`);
      console.log(`    创建时间: ${version.created_at.toLocaleString()}`);
    });
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ 所有查询测试完成！');
    
  } catch (error) {
    console.error('❌ 查询测试失败:', error);
  } finally {
    await client.close();
    console.log('🔌 MongoDB 连接已关闭');
  }
}

// 执行测试
testQueries().catch(console.error);
