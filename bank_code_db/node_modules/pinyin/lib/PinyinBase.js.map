{"version": 3, "file": "PinyinBase.js", "sourceRoot": "", "sources": ["../src/PinyinBase.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAAqC,CAAC,YAAY;AAClD,qEAA+C,CAAC,UAAU;AAC1D,6CAAwC;AACxC,mCAAmC;AACnC,2DAA+C;AAC/C,6EAAgE;AAChE,+BAAoE;AACpE,uCAAiE;AAQjE;IAAA;QACE,iBAAiB;QACjB,iBAAiB;QACjB,eAAU,GAAG,4BAAiB,CAAC,IAAI,CAAC;QACpC,gBAAW,GAAG,4BAAiB,CAAC,KAAK,CAAC;QACtC,gBAAW,GAAG,4BAAiB,CAAC,KAAK,CAAC;QACtC,iBAAY,GAAG,4BAAiB,CAAC,MAAM,CAAC;QACxC,mBAAc,GAAG,4BAAiB,CAAC,QAAQ,CAAC;QAC5C,uBAAkB,GAAG,4BAAiB,CAAC,YAAY,CAAC;QACpD,mBAAc,GAAG,4BAAiB,CAAC,QAAQ,CAAC;QAE5C,iBAAiB;QACjB,eAAe;QACf,gBAAW,GAAG,2BAAgB,CAAC,MAAM,CAAC;QACtC,iBAAY,GAAG,2BAAgB,CAAC,OAAO,CAAC;IAiQ1C,CAAC;IAhQC,+CAA+C;IAE/C;;OAEG;IACH,2BAAM,GAAN,UAAO,IAAY,EAAE,OAAwB;QAC3C,IAAG,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC3B,OAAO,EAAE,CAAC;SACX;QACD,IAAM,GAAG,GAAsB,IAAA,yBAAkB,EAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,GAAG,CAAC;QACR,IAAI,GAAG,CAAC,IAAI,KAAK,2BAAgB,CAAC,OAAO,EAAE;YACzC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SACtC;aAAM;YACL,oCAAoC;YACpC,IAAI,GAAG,CAAC,OAAO,EAAE;gBACf,aAAa;gBACb,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACtC;iBAAM;gBACL,qCAAqC;gBACrC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACrC;SACF;QACD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE;YACpB,GAAG,GAAG,IAAA,cAAO,EAAC,GAAG,CAAC,CAAC;SACpB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACH,kCAAa,GAAb,UAAc,IAAY,EAAE,OAA0B;QACpD,IAAM,GAAG,GAAe,EAAE,CAAC;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAG,iBAAO,CAAC,aAAa,CAAC,EAAC;gBACxB,gBAAgB;gBAChB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnB,MAAM,GAAG,EAAE,CAAC,CAAC,aAAa;iBAC3B;gBACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;aAC9C;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC;aACjB;SACF;QAED,eAAe;QACf,IAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;YACnB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACnB,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;SACzC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,kCAAa,GAAb,UAAc,GAAW,EAAE,OAA0B;QACnD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,EAAE,CAAC;SACX;QACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;SACnD;QAED,IAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,CAAC,iBAAO,CAAC,OAAO,CAAC,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,CAAC;SACd;QAED,IAAM,GAAG,GAAG,iBAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,IAAG,CAAC,OAAO,CAAC,SAAS,EAAC;YACpB,OAAO,CAAC,IAAA,gBAAO,EAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;SACzC;QAED,kCAAkC;QAClC,IAAM,SAAS,GAA0B,EAAE,CAAC;QAC5C,IAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAC;YACxC,IAAM,EAAE,GAAG,IAAA,gBAAO,EAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAG,IAAA,aAAM,EAAC,SAAS,EAAE,EAAE,CAAC,EAAC;gBACvB,SAAS;aACV;YACD,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YAEnB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAClB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,4BAAO,GAAP,UAAQ,IAAY,EAAE,WAA4B;QAChD,OAAO,IAAA,qBAAO,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,mCAAc,GAAd,UAAe,IAAY,EAAE,OAA0B;QACrD,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,GAAG,GAAe,EAAE,CAAC;QACzB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,IAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAG,iBAAO,CAAC,aAAa,CAAC,EAAC;gBACxB,6BAA6B;gBAC7B,IAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;oBACnB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnB,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;iBACzC;gBAED,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC;oBAC/B,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;oBACpC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAExC,IAAI,OAAO,CAAC,KAAK,EAAE;oBACjB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;iBACrC;qBAAM;oBACL,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBAC1B;aAEF;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC;aACjB;SACF;QAED,eAAe;QACf,IAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;YACnB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACnB,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;SACzC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACH,mCAAc,GAAd,UAAe,OAAe,EAAE,OAA0B;QACxD,IAAM,EAAE,GAAe,EAAE,CAAC;QAC1B,IAAI,IAAA,aAAM,EAAC,sBAAY,EAAE,OAAO,CAAC,EAAE;YACjC,uBAAuB;YACvB,sBAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAS,IAAc,EAAE,GAAW;gBAChE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gBACb,IAAI,OAAO,CAAC,SAAS,EAAE;oBACrB,IAAI,CAAC,OAAO,CAAC,UAAS,OAAO,EAAE,QAAQ;wBACrC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAA,gBAAO,EAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBACtD,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAA,gBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;iBAC9C;YACH,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;aAClD;SACF;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,iCAAY,GAAZ,UAAa,OAAmB;QAC9B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;SACnB;QAED,IAAM,OAAO,GAAG,IAAA,YAAK,EAAC,OAAO,CAAC,CAAC;QAE/B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS;IACT,mCAAc,GAAd,UAAe,IAAY,EAAE,OAA0B;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,OAAO;IACP,qCAAgB,GAAhB,UAAiB,IAAY,EAAE,OAA0B;QACvD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,MAAM,GAAe,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,IAAI,IAAA,aAAM,EAAC,0BAAyB,EAAE,QAAQ,CAAC,EAAE;gBAC/C,IAAI,WAAW,IAAI,CAAC,GAAG,CAAC,EAAE;oBACxB,MAAM,GAAG,MAAM,CAAC,MAAM,CACpB,IAAI,CAAC,cAAc,CACjB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,EAC9B,OAAO,CACR,CACF,CAAC;iBACH;gBACD,IAAM,GAAG,GAAG,0BAAyB,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAC,IAAc;oBACjE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAC,EAAU,IAAK,OAAA,IAAA,gBAAO,EAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAE5B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACV,WAAW,GAAG,CAAC,CAAC;aACjB;SACF;QACD,eAAe;QACf,MAAM,GAAG,MAAM,CAAC,MAAM,CACpB,IAAI,CAAC,cAAc,CACjB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,EAChC,OAAO,CACR,CACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO;IACP,mCAAc,GAAd,UAAe,IAAY,EAAE,OAA0B;QACrD,IAAI,MAAM,GAAe,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,IAAA,aAAM,EAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE;gBACnC,IAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAC,IAAc;oBACrD,OAAO,IAAI,CAAC,GAAG,CAAC,UAAC,EAAU,IAAK,OAAA,IAAA,gBAAO,EAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aAC7B;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;aAChD;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,4BAAO,GAAP,UAAQ,IAAY,EAAE,IAAY;QAChC,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,4BAAO,GAAP,UAAQ,GAAe;QACrB,OAAO,IAAA,cAAO,EAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IACH,iBAAC;AAAD,CAAC,AA/QD,IA+QC;;AAED,SAAgB,iBAAiB,CAAC,EAAc;IAC9C,IAAM,MAAM,GAAY,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3C,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAErC,gCAAgC;IAChC,MAAM,CAAC,UAAU,GAAG,4BAAiB,CAAC,IAAI,CAAC;IAC3C,MAAM,CAAC,WAAW,GAAG,4BAAiB,CAAC,KAAK,CAAC;IAC7C,MAAM,CAAC,WAAW,GAAG,4BAAiB,CAAC,KAAK,CAAC;IAC7C,MAAM,CAAC,YAAY,GAAG,4BAAiB,CAAC,MAAM,CAAC;IAC/C,MAAM,CAAC,cAAc,GAAG,4BAAiB,CAAC,QAAQ,CAAC;IACnD,MAAM,CAAC,kBAAkB,GAAG,4BAAiB,CAAC,YAAY,CAAC;IAC3D,MAAM,CAAC,cAAc,GAAG,4BAAiB,CAAC,QAAQ,CAAC;IAEnD,8BAA8B;IAC9B,MAAM,CAAC,WAAW,GAAG,2BAAgB,CAAC,MAAM,CAAC;IAC7C,MAAM,CAAC,YAAY,GAAG,2BAAgB,CAAC,OAAO,CAAC;IAC/C,sDAAsD;IAEtD,OAAO,MAAM,CAAC;AAChB,CAAC;AApBD,8CAoBC"}