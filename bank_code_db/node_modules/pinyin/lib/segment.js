"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.segment = void 0;
var nodeRsJiebaLoaded = false; // @node-rs/jieba 加载词典。
var segmentit; // segmentit 加载词典。
var hansIntlSegmenter; // Intl.Segmenter
/**
 * TODO: 分词并带词性信息，需要调整 segment_pinyin 方法。
 * 分词并标注词性。
 */
function segment(hans, segment) {
    try {
        // @node-rs/jieba (Rust)
        if (segment === "@node-rs/jieba") {
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            var _a = require("@node-rs/jieba"), load = _a.load, cut = _a.cut /*, tag */;
            if (!nodeRsJiebaLoaded) {
                nodeRsJiebaLoaded = true;
                load();
            }
            return cut(hans, false);
            // return tag(hans);
        }
        // segmentit (Node.js)
        if (segment === "segmentit") {
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            var _b = require("segmentit"), Segment = _b.Segment, useDefault = _b.useDefault;
            if (!segmentit) {
                segmentit = useDefault(new Segment());
            }
            return segmentit.doSegment(hans, {
                simple: true,
            });
        }
        // Intl.Segmenter
        if (segment === "Intl.Segmenter") {
            if (typeof (Intl === null || Intl === void 0 ? void 0 : Intl.Segmenter) === "function") {
                if (!hansIntlSegmenter) {
                    hansIntlSegmenter = new Intl.Segmenter("zh-Hans-CN", {
                        granularity: "word",
                    });
                }
                return __spreadArray([], __read(hansIntlSegmenter.segment(hans)), false).map(function (s) { return s.segment; });
            }
        }
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        var nodejieba = require("nodejieba");
        // 默认使用 nodejieba (C++)
        // return nodejieba.tag(hans);
        // nodejieba 定义的类型返回值错误，先忽略。
        return nodejieba.cutSmall(hans, 4);
    }
    catch (ex) {
        return [hans];
    }
}
exports.segment = segment;
//# sourceMappingURL=segment.js.map