{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../src/format.ts"], "names": [], "mappings": ";;;AAAA,uCAA0E;AAC1E,+BAAgC;AAEhC;;;;GAIG;AACH,SAAS,QAAQ,CAAC,EAAU;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAC;QAC9C,IAAI,EAAE,CAAC,OAAO,CAAC,mBAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACjC,OAAO,mBAAQ,CAAC,CAAC,CAAC,CAAC;SACpB;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,IAAM,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,0BAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;AAChG,IAAM,QAAQ,GAAG,sBAAsB,CAAC;AAExC;;;;;;GAMG;AACH,SAAgB,OAAO,CAAC,MAAc,EAAE,KAAwB;IAC9D,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,MAAM;IACrB,IAAI,YAAoB,CAAC;IACzB,IAAI,EAAU,CAAC;IACf,QAAO,KAAK,EAAE;QACd,KAAK,4BAAiB,CAAC,QAAQ;YAC7B,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE1B,KAAK,4BAAiB,CAAC,YAAY;YACjC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,IAAA,aAAM,EAAC,0BAAe,EAAE,YAAY,CAAC,EAAE;gBACzC,YAAY,GAAG,0BAAe,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACxD;YACD,OAAO,YAAY,CAAC;QAEtB,KAAK,4BAAiB,CAAC,MAAM;YAC3B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,WAAmB;gBAChF,OAAO,0BAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QAEL,KAAK,4BAAiB,CAAC,QAAQ;YAC7B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,WAAmB;gBAChF,OAAO,0BAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEnB,KAAK,4BAAiB,CAAC,KAAK;YAC1B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,WAAmB;gBAChF,OAAO,0BAAe,CAAC,WAAW,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QAEL,KAAK,4BAAiB,CAAC,KAAK;YAC1B,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,EAAU;gBACrE,QAAQ;gBACR,IAAI,GAAG,0BAAe,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAEnD,OAAO,0BAAe,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,GAAG,IAAI,CAAC;QAEnB,KAAK,4BAAiB,CAAC,IAAI,CAAC;QAC5B;YACE,OAAO,MAAM,CAAC;KACf;AACH,CAAC;AA3CD,0BA2CC"}