{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,uCAAkF;AAElF,SAAgB,MAAM,CAAC,GAAQ,EAAE,GAAW;IACxC,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC1D,CAAC;AAFD,wBAEC;AAED,IAAM,cAAc,GAAmC,IAAI,GAAG,CAAC;IAC7D,CAAE,MAAM,EAAE,4BAAiB,CAAC,IAAI,CAAE;IAClC,CAAE,MAAM,EAAE,4BAAiB,CAAC,IAAI,CAAE;IAClC,CAAE,GAAG,EAAE,4BAAiB,CAAC,IAAI,CAAE;IAE/B,CAAE,OAAO,EAAE,4BAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,OAAO,EAAE,4BAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,GAAG,EAAE,4BAAiB,CAAC,KAAK,CAAE;IAEhC,CAAE,OAAO,EAAE,4BAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,OAAO,EAAE,4BAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,GAAG,EAAE,4BAAiB,CAAC,KAAK,CAAE;IAEhC,CAAE,cAAc,EAAE,4BAAiB,CAAC,YAAY,CAAE;IAClD,CAAE,cAAc,EAAE,4BAAiB,CAAC,YAAY,CAAE;IAClD,CAAE,GAAG,EAAE,4BAAiB,CAAC,YAAY,CAAE;IAEvC,CAAE,UAAU,EAAE,4BAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,UAAU,EAAE,4BAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,GAAG,EAAE,4BAAiB,CAAC,QAAQ,CAAE;IAEnC,CAAE,QAAQ,EAAE,4BAAiB,CAAC,MAAM,CAAE;IACtC,CAAE,QAAQ,EAAE,4BAAiB,CAAC,MAAM,CAAE;IACtC,CAAE,GAAG,EAAE,4BAAiB,CAAC,MAAM,CAAE;IAEjC,CAAE,UAAU,EAAE,4BAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,UAAU,EAAE,4BAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,GAAG,EAAE,4BAAiB,CAAC,QAAQ,CAAE;CACpC,CAAC,CAAC;AAEH,2BAA2B;AAC3B,SAAgB,kBAAkB,CAAC,KAAoB;IACrD,IAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACxB,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC,CAAsB,CAAC;KACnD;IACD,OAAO,4BAAiB,CAAC,IAAI,CAAC;AAChC,CAAC;AAND,gDAMC;AAED,IAAM,aAAa,GAAkC,IAAI,GAAG,CAAC;IAC3D,CAAE,QAAQ,EAAE,2BAAgB,CAAC,MAAM,CAAE;IACrC,CAAE,QAAQ,EAAE,2BAAgB,CAAC,MAAM,CAAE;IACrC,CAAE,SAAS,EAAE,2BAAgB,CAAC,OAAO,CAAE;IACvC,CAAE,SAAS,EAAE,2BAAgB,CAAC,OAAO,CAAE;CACxC,CAAC,CAAC;AAEH,2BAA2B;AAC3B,SAAgB,iBAAiB,CAAC,IAAkB;IAClD,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IACvB,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACxB,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,CAAqB,CAAC;KACjD;IACD,OAAO,2BAAgB,CAAC,MAAM,CAAC;AACjC,CAAC;AAND,8CAMC;AAED,SAAgB,kBAAkB,CAAC,OAAwB;IACzD,IAAI,OAAO,GAA+B,SAAS,CAAC;IACpD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE;QACpB,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,MAAK,IAAI,EAAE;YAC7B,OAAO,GAAG,gBAAgB,CAAC;SAC5B;aAAM;YACL,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC3B;KACF;IACD,IAAM,GAAG,yBACJ,0BAAe,KAClB,KAAK,EAAE,kBAAkB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,EACzC,IAAI,EAAE,iBAAiB,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAC,EACtC,OAAO,SAAA,EACP,SAAS,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,KAAK,EACtC,KAAK,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,KAAI,KAAK,GAC/B,CAAC;IACF,OAAO,GAAG,CAAC;AACb,CAAC;AAlBD,gDAkBC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,EAAY,EAAE,EAAY;IACpD,IAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,OAAO,EAAE,CAAC;KACX;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,OAAO,EAAE,CAAC;KACX;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAdD,kCAcC;AAED;;;;;;;;GAQG;AACH,SAAgB,KAAK,CAAC,GAAe;IACnC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;KACf;IACD,IAAI,MAAM,GAAa,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1C,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAZD,sBAYC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,EAAuB,EAAE,EAAY;IACjE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;KACjE;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;KACX;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;KACX;IACD,IAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxB,MAAM,CAAC,IAAI,wCAAK,EAAE,CAAC,CAAC,CAAC,YAAE,EAAE,CAAC,CAAC,CAAC,UAAE,CAAC;aAChC;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AArBD,sCAqBC;AAED,SAAgB,OAAO,CAAC,GAAe;IACrC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACjB;IACD,IAAI,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QAC1C,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAZD,0BAYC"}