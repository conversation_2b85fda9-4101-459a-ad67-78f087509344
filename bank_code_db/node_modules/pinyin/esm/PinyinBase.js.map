{"version": 3, "file": "PinyinBase.js", "sourceRoot": "", "sources": ["../src/PinyinBase.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,gBAAgB,CAAC,CAAC,YAAY;AAClD,OAAO,YAAY,MAAM,qBAAqB,CAAC,CAAC,UAAU;AAC1D,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AACnC,OAAO,iBAAiB,MAAM,gBAAgB,CAAC;AAC/C,OAAO,yBAAyB,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;AAQjE,MAAM,CAAC,OAAO,OAAO,UAAU;IAC7B,iBAAiB;IACjB,iBAAiB;IACjB,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;IACpC,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;IACtC,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;IACtC,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;IACxC,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAC5C,kBAAkB,GAAG,iBAAiB,CAAC,YAAY,CAAC;IACpD,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAE5C,iBAAiB;IACjB,eAAe;IACf,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACtC,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC;IACxC,+CAA+C;IAE/C;;OAEG;IACH,MAAM,CAAC,IAAY,EAAE,OAAwB;QAC3C,IAAG,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC3B,OAAO,EAAE,CAAC;SACX;QACD,MAAM,GAAG,GAAsB,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,GAAG,CAAC;QACR,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;YACzC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SACtC;aAAM;YACL,oCAAoC;YACpC,IAAI,GAAG,CAAC,OAAO,EAAE;gBACf,aAAa;gBACb,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACtC;iBAAM;gBACL,qCAAqC;gBACrC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACrC;SACF;QACD,IAAI,OAAO,EAAE,OAAO,EAAE;YACpB,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;SACpB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAAY,EAAE,OAA0B;QACpD,MAAM,GAAG,GAAe,EAAE,CAAC;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAG,OAAO,CAAC,aAAa,CAAC,EAAC;gBACxB,gBAAgB;gBAChB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnB,MAAM,GAAG,EAAE,CAAC,CAAC,aAAa;iBAC3B;gBACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;aAC9C;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC;aACjB;SACF;QAED,eAAe;QACf,IAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;YACnB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACnB,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;SACzC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,GAAW,EAAE,OAA0B;QACnD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,EAAE,CAAC;SACX;QACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;SACnD;QAED,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,CAAC;SACd;QAED,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,IAAG,CAAC,OAAO,CAAC,SAAS,EAAC;YACpB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;SACzC;QAED,kCAAkC;QAClC,MAAM,SAAS,GAA0B,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAC;YACxC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAG,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC,EAAC;gBACvB,SAAS;aACV;YACD,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YAEnB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAClB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,CAAC,IAAY,EAAE,WAA4B;QAChD,OAAO,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY,EAAE,OAA0B;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,GAAG,GAAe,EAAE,CAAC;QACzB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAG,OAAO,CAAC,aAAa,CAAC,EAAC;gBACxB,6BAA6B;gBAC7B,IAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;oBACnB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnB,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;iBACzC;gBAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC;oBAC/B,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;oBACpC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAExC,IAAI,OAAO,CAAC,KAAK,EAAE;oBACjB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;iBACrC;qBAAM;oBACL,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBAC1B;aAEF;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC;aACjB;SACF;QAED,eAAe;QACf,IAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAC;YACnB,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACnB,MAAM,GAAG,EAAE,CAAC,CAAC,2BAA2B;SACzC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACH,cAAc,CAAC,OAAe,EAAE,OAA0B;QACxD,MAAM,EAAE,GAAe,EAAE,CAAC;QAC1B,IAAI,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE;YACjC,uBAAuB;YACvB,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAS,IAAc,EAAE,GAAW;gBAChE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gBACb,IAAI,OAAO,CAAC,SAAS,EAAE;oBACrB,IAAI,CAAC,OAAO,CAAC,UAAS,OAAO,EAAE,QAAQ;wBACrC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBACtD,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;iBAC9C;YACH,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;aAClD;SACF;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,YAAY,CAAC,OAAmB;QAC9B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;SACnB;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;QAE/B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS;IACT,cAAc,CAAC,IAAY,EAAE,OAA0B;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,OAAO;IACP,gBAAgB,CAAC,IAAY,EAAE,OAA0B;QACvD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,MAAM,GAAe,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,IAAI,MAAM,CAAC,yBAAyB,EAAE,QAAQ,CAAC,EAAE;gBAC/C,IAAI,WAAW,IAAI,CAAC,GAAG,CAAC,EAAE;oBACxB,MAAM,GAAG,MAAM,CAAC,MAAM,CACpB,IAAI,CAAC,cAAc,CACjB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,EAC9B,OAAO,CACR,CACF,CAAC;iBACH;gBACD,MAAM,GAAG,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAc,EAAE,EAAE;oBACrE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAU,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAE5B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACV,WAAW,GAAG,CAAC,CAAC;aACjB;SACF;QACD,eAAe;QACf,MAAM,GAAG,MAAM,CAAC,MAAM,CACpB,IAAI,CAAC,cAAc,CACjB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,EAChC,OAAO,CACR,CACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO;IACP,cAAc,CAAC,IAAY,EAAE,OAA0B;QACrD,IAAI,MAAM,GAAe,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE;gBACnC,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAc,EAAE,EAAE;oBACzD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAU,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aAC7B;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;aAChD;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,IAAY,EAAE,IAAY;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,CAAC,GAAe;QACrB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAED,MAAM,UAAU,iBAAiB,CAAC,EAAc;IAC9C,MAAM,MAAM,GAAY,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3C,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAErC,gCAAgC;IAChC,MAAM,CAAC,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;IAC3C,MAAM,CAAC,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;IAC7C,MAAM,CAAC,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;IAC7C,MAAM,CAAC,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;IAC/C,MAAM,CAAC,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IACnD,MAAM,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,YAAY,CAAC;IAC3D,MAAM,CAAC,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAEnD,8BAA8B;IAC9B,MAAM,CAAC,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAC7C,MAAM,CAAC,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC;IAC/C,sDAAsD;IAEtD,OAAO,MAAM,CAAC;AAChB,CAAC"}