declare const _default: {
    万俟: string[][];
    上官: string[][];
    东方: string[][];
    东郭: string[][];
    东门: string[][];
    乐正: string[][];
    亓官: string[][];
    仉督: string[][];
    令狐: string[][];
    仲孙: string[][];
    公冶: string[][];
    公孙: string[][];
    公羊: string[][];
    公良: string[][];
    公西: string[][];
    单于: string[][];
    南宫: string[][];
    南门: string[][];
    司寇: string[][];
    司徒: string[][];
    司空: string[][];
    司马: string[][];
    呼延: string[][];
    壤驷: string[][];
    夏侯: string[][];
    太叔: string[][];
    夹谷: string[][];
    子车: string[][];
    宇文: string[][];
    宗政: string[][];
    宰父: string[][];
    尉迟: string[][];
    左丘: string[][];
    巫马: string[][];
    慕容: string[][];
    拓跋: string[][];
    梁丘: string[][];
    榖梁: string[][];
    欧阳: string[][];
    段干: string[][];
    淳于: string[][];
    漆雕: string[][];
    澹台: string[][];
    濮阳: string[][];
    申屠: string[][];
    百里: string[][];
    皇甫: string[][];
    端木: string[][];
    第五: string[][];
    羊舌: string[][];
    西门: string[][];
    诸葛: string[][];
    赫连: string[][];
    轩辕: string[][];
    钟离: string[][];
    长孙: string[][];
    闻人: string[][];
    闾丘: string[][];
    颛孙: string[][];
    鲜于: string[][];
};
export default _default;
