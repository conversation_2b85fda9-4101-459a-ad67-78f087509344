{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAElF,MAAM,UAAU,MAAM,CAAC,GAAQ,EAAE,GAAW;IACxC,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC1D,CAAC;AAED,MAAM,cAAc,GAAmC,IAAI,GAAG,CAAC;IAC7D,CAAE,MAAM,EAAE,iBAAiB,CAAC,IAAI,CAAE;IAClC,CAAE,MAAM,EAAE,iBAAiB,CAAC,IAAI,CAAE;IAClC,CAAE,GAAG,EAAE,iBAAiB,CAAC,IAAI,CAAE;IAE/B,CAAE,OAAO,EAAE,iBAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,OAAO,EAAE,iBAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,GAAG,EAAE,iBAAiB,CAAC,KAAK,CAAE;IAEhC,CAAE,OAAO,EAAE,iBAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,OAAO,EAAE,iBAAiB,CAAC,KAAK,CAAE;IACpC,CAAE,GAAG,EAAE,iBAAiB,CAAC,KAAK,CAAE;IAEhC,CAAE,cAAc,EAAE,iBAAiB,CAAC,YAAY,CAAE;IAClD,CAAE,cAAc,EAAE,iBAAiB,CAAC,YAAY,CAAE;IAClD,CAAE,GAAG,EAAE,iBAAiB,CAAC,YAAY,CAAE;IAEvC,CAAE,UAAU,EAAE,iBAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,UAAU,EAAE,iBAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,GAAG,EAAE,iBAAiB,CAAC,QAAQ,CAAE;IAEnC,CAAE,QAAQ,EAAE,iBAAiB,CAAC,MAAM,CAAE;IACtC,CAAE,QAAQ,EAAE,iBAAiB,CAAC,MAAM,CAAE;IACtC,CAAE,GAAG,EAAE,iBAAiB,CAAC,MAAM,CAAE;IAEjC,CAAE,UAAU,EAAE,iBAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,UAAU,EAAE,iBAAiB,CAAC,QAAQ,CAAE;IAC1C,CAAE,GAAG,EAAE,iBAAiB,CAAC,QAAQ,CAAE;CACpC,CAAC,CAAC;AAEH,2BAA2B;AAC3B,MAAM,UAAU,kBAAkB,CAAC,KAAoB;IACrD,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACxB,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACzB,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC,CAAsB,CAAC;KACnD;IACD,OAAO,iBAAiB,CAAC,IAAI,CAAC;AAChC,CAAC;AAED,MAAM,aAAa,GAAkC,IAAI,GAAG,CAAC;IAC3D,CAAE,QAAQ,EAAE,gBAAgB,CAAC,MAAM,CAAE;IACrC,CAAE,QAAQ,EAAE,gBAAgB,CAAC,MAAM,CAAE;IACrC,CAAE,SAAS,EAAE,gBAAgB,CAAC,OAAO,CAAE;IACvC,CAAE,SAAS,EAAE,gBAAgB,CAAC,OAAO,CAAE;CACxC,CAAC,CAAC;AAEH,2BAA2B;AAC3B,MAAM,UAAU,iBAAiB,CAAC,IAAkB;IAClD,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IACvB,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACxB,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,CAAqB,CAAC;KACjD;IACD,OAAO,gBAAgB,CAAC,MAAM,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,OAAwB;IACzD,IAAI,OAAO,GAA+B,SAAS,CAAC;IACpD,IAAI,OAAO,EAAE,OAAO,EAAE;QACpB,IAAI,OAAO,EAAE,OAAO,KAAK,IAAI,EAAE;YAC7B,OAAO,GAAG,gBAAgB,CAAC;SAC5B;aAAM;YACL,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC3B;KACF;IACD,MAAM,GAAG,GAAsB;QAC7B,GAAG,eAAe;QAClB,KAAK,EAAE,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC;QACzC,IAAI,EAAE,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC;QACtC,OAAO;QACP,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,KAAK;QACtC,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,KAAK;KAC/B,CAAC;IACF,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,EAAY,EAAE,EAAY;IACpD,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,OAAO,EAAE,CAAC;KACX;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,OAAO,EAAE,CAAC;KACX;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,KAAK,CAAC,GAAe;IACnC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;KACf;IACD,IAAI,MAAM,GAAa,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1C,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,EAAuB,EAAE,EAAY;IACjE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;KACjE;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;KACX;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;QACd,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;KACX;IACD,MAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxB,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,GAAe;IACrC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,EAAE,CAAC;KACX;IACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QACpB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACjB;IACD,IAAI,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QAC1C,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}