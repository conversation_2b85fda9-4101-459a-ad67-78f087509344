{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../src/format.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC1E,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC;;;;GAIG;AACH,SAAS,QAAQ,CAAC,EAAU;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAC;QAC9C,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACjC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;SACpB;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;AAChG,MAAM,QAAQ,GAAG,sBAAsB,CAAC;AAExC;;;;;;GAMG;AACH,MAAM,UAAU,OAAO,CAAC,MAAc,EAAE,KAAwB;IAC9D,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,MAAM;IACrB,IAAI,YAAoB,CAAC;IACzB,IAAI,EAAU,CAAC;IACf,QAAO,KAAK,EAAE;QACd,KAAK,iBAAiB,CAAC,QAAQ;YAC7B,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE1B,KAAK,iBAAiB,CAAC,YAAY;YACjC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,MAAM,CAAC,eAAe,EAAE,YAAY,CAAC,EAAE;gBACzC,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACxD;YACD,OAAO,YAAY,CAAC;QAEtB,KAAK,iBAAiB,CAAC,MAAM;YAC3B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,WAAmB;gBAChF,OAAO,eAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;QAEL,KAAK,iBAAiB,CAAC,QAAQ;YAC7B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,WAAmB;gBAChF,OAAO,eAAe,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEnB,KAAK,iBAAiB,CAAC,KAAK;YAC1B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,WAAmB;gBAChF,OAAO,eAAe,CAAC,WAAW,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QAEL,KAAK,iBAAiB,CAAC,KAAK;YAC1B,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,UAAS,EAAU,EAAE,EAAU;gBACrE,QAAQ;gBACR,IAAI,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAEnD,OAAO,eAAe,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YACH,OAAO,EAAE,GAAG,IAAI,CAAC;QAEnB,KAAK,iBAAiB,CAAC,IAAI,CAAC;QAC5B;YACE,OAAO,MAAM,CAAC;KACf;AACH,CAAC"}