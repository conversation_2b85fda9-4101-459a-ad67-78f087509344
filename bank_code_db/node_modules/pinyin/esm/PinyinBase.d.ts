import { ENUM_PINYIN_MODE, ENUM_PINYIN_STYLE } from "./constant";
import type { IPinyinAllOptions, IPinyinOptions, IPinyinSegment, IPinyin } from "./declare";
export default class PinyinBase {
    STYLE_TONE: ENUM_PINYIN_STYLE;
    STYLE_TONE2: ENUM_PINYIN_STYLE;
    STYLE_TO3NE: ENUM_PINYIN_STYLE;
    STYLE_NORMAL: ENUM_PINYIN_STYLE;
    STYLE_INITIALS: ENUM_PINYIN_STYLE;
    STYLE_FIRST_LETTER: ENUM_PINYIN_STYLE;
    STYLE_PASSPORT: ENUM_PINYIN_STYLE;
    MODE_NORMAL: ENUM_PINYIN_MODE;
    MODE_SURNAME: ENUM_PINYIN_MODE;
    /**
     * 拼音转换入口。
     */
    pinyin(hans: string, options?: IPinyinOptions): string[][];
    /**
     * 不使用分词算法的拼音转换。
     */
    normal_pinyin(hans: string, options: IPinyinAllOptions): string[][];
    /**
     * 单字拼音转换。
     * @param {String} han, 单个汉字
     * @return {Array} 返回拼音列表，多音字会有多个拼音项。
     */
    single_pinyin(han: string, options: IPinyinAllOptions): string[];
    segment(hans: string, segmentType?: IPinyinSegment): string[];
    /**
     * 将文本分词，并转换成拼音。
     */
    segment_pinyin(hans: string, options: IPinyinAllOptions): string[][];
    /**
     * 词语注音
     * @param {String} phrases, 指定的词组。
     * @param {Object} options, 选项。
     * @return {Array}
     */
    phrases_pinyin(phrases: string, options: IPinyinAllOptions): string[][];
    groupPhrases(phrases: string[][]): string[];
    surname_pinyin(hans: string, options: IPinyinAllOptions): string[][];
    compound_surname(hans: string, options: IPinyinAllOptions): string[][];
    single_surname(hans: string, options: IPinyinAllOptions): string[][];
    /**
     * 比较两个汉字转成拼音后的排序顺序，可以用作默认的拼音排序算法。
     *
     * @param {String} hanA 汉字字符串 A。
     * @return {String} hanB 汉字字符串 B。
     * @return {Number} 返回 -1，0，或 1。
     */
    compare(hanA: string, hanB: string): number;
    compact(pys: string[][]): string[][];
}
export declare function getPinyinInstance(py: PinyinBase): IPinyin;
