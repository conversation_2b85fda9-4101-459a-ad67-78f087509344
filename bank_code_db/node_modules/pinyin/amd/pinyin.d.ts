declare module "data/dict-zi" {
    const dict: Record<number, string>;
    export default dict;
}
declare module "data/phrases-dict" {
    const phrases_dict: Record<string, string[][]>;
    export default phrases_dict;
}
declare module "constant" {
    import type { IPinyinAllOptions } from "declare";
    export enum ENUM_PINYIN_STYLE {
        NORMAL = 0,
        TONE = 1,
        TONE2 = 2,
        TO3NE = 5,
        INITIALS = 3,
        FIRST_LETTER = 4,
        PASSPORT = 6
    }
    export enum ENUM_PINYIN_MODE {
        NORMAL = 0,
        SURNAME = 1
    }
    export const DEFAULT_OPTIONS: IPinyinAllOptions;
    export const PHONETIC_SYMBOL: Record<string, string>;
    export const INITIALS: string[];
    export const FINALS: string[];
}
declare module "declare" {
    import { ENUM_PINYIN_STYLE, ENUM_PINYIN_MODE } from "constant";
    export interface IPinyin {
        (han: string, options?: IPinyinOptions): string[][];
        compare: (a: string, b: string) => number;
        compact: (arr: string[][]) => string[][];
        STYLE_TONE: ENUM_PINYIN_STYLE;
        STYLE_TONE2: ENUM_PINYIN_STYLE;
        STYLE_TO3NE: ENUM_PINYIN_STYLE;
        STYLE_NORMAL: ENUM_PINYIN_STYLE;
        STYLE_INITIALS: ENUM_PINYIN_STYLE;
        STYLE_FIRST_LETTER: ENUM_PINYIN_STYLE;
        STYLE_PASSPORT: ENUM_PINYIN_STYLE;
        MODE_NORMAL: ENUM_PINYIN_MODE;
        MODE_SURNAME: ENUM_PINYIN_MODE;
    }
    export type IPinyinStyle = ENUM_PINYIN_STYLE | "normal" | "tone" | "tone2" | "to3ne" | "initials" | "first_letter" | "passport" | // 推荐使用小写，和输出的拼音一致
    "NORMAL" | "TONE" | "TONE2" | "TO3NE" | "INITIALS" | "FIRST_LETTER" | "PASSPORT" | // 方便老版本迁移
    0 | 1 | 2 | 5 | 3 | 4;
    export type IPinyinMode = ENUM_PINYIN_MODE | "normal" | "surname" | "NORMAL" | "SURNAME";
    export type IPinyinSegment = "nodejieba" | "segmentit" | "@node-rs/jieba" | "Intl.Segmenter";
    export interface IPinyinAllOptions {
        style: ENUM_PINYIN_STYLE;
        mode: ENUM_PINYIN_MODE;
        segment?: IPinyinSegment;
        heteronym: boolean;
        group: boolean;
        compact: boolean;
    }
    export interface IPinyinOptions {
        style?: IPinyinStyle;
        mode?: IPinyinMode;
        segment?: IPinyinSegment | boolean;
        heteronym?: boolean;
        group?: boolean;
        compact?: boolean;
    }
}
declare module "segment-web" {
    import type { IPinyinSegment } from "declare";
    /**
     * TODO: 分词并带词性信息，需要调整 segment_pinyin 方法。
     * 分词并标注词性。
     */
    export function segment(hans: string, segment?: IPinyinSegment): string[];
}
declare module "util" {
    import type { IPinyinAllOptions, IPinyinOptions, IPinyinStyle, IPinyinMode } from "declare";
    import { ENUM_PINYIN_STYLE, ENUM_PINYIN_MODE } from "constant";
    export function hasKey(obj: any, key: string): boolean;
    export function convertPinyinStyle(style?: IPinyinStyle): ENUM_PINYIN_STYLE;
    export function convertPinyinMode(mode?: IPinyinMode): ENUM_PINYIN_MODE;
    export function convertUserOptions(options?: IPinyinOptions): IPinyinAllOptions;
    /**
     * 组合 2 个拼音数组。
     * @param {string[]} a1 第一个数组，形如 ["zhāo", "cháo"]
     * @param {string[]} a2 字符串型数组。形如 ["yáng"]
     * @return {string[]} 组合后的一维数组，如上可得 ["zhāoyáng", "cháoyáng"]
     */
    export function combo2array(a1: string[], a2: string[]): string[];
    /**
     * 合并二维元祖。
     * @param {string[][]} arr 二维元祖 [["zhāo", "cháo"], ["yáng"], ["dōng"], ["shēng"]]
     * @return {string[]} 返回二维字符串组合数组。形如
     *  [
     *    ["zhāoyáng"], ["dōng"], ["shēng"],
     *    ["cháoyáng"], ["dōng"], ["shēng"]
     *  ]
     */
    export function combo(arr: string[][]): string[];
    /**
     * 组合两个拼音数组，形成一个新的二维数组
     * @param {string[]|string[][]} arr1 eg: ["hai", "huan"]
     * @param {string[]} arr2 eg: ["qian"]
     * @returns {string[][]} 组合后的二维数组，eg: [ ["hai", "qian"], ["huan", "qian"] ]
     */
    export function compact2array(a1: string[] | string[][], a2: string[]): string[][];
    export function compact(arr: string[][]): string[][];
}
declare module "format" {
    import { ENUM_PINYIN_STYLE } from "constant";
    /**
     * 格式化拼音风格。
     *
     * @param {string} pinyin TONE 风格的拼音。
     * @param {ENUM_PINYIN_STYLE} style 目标转换的拼音风格。
     * @return {string} 转换后的拼音。
     */
    export function toFixed(pinyin: string, style: ENUM_PINYIN_STYLE): string;
}
declare module "data/surname" {
    const _default: {
        赵: string[][];
        钱: string[][];
        孙: string[][];
        李: string[][];
        周: string[][];
        吴: string[][];
        郑: string[][];
        王: string[][];
        冯: string[][];
        陈: string[][];
        褚: string[][];
        卫: string[][];
        蒋: string[][];
        沈: string[][];
        韩: string[][];
        杨: string[][];
        朱: string[][];
        秦: string[][];
        尤: string[][];
        许: string[][];
        何: string[][];
        吕: string[][];
        施: string[][];
        张: string[][];
        孔: string[][];
        曹: string[][];
        严: string[][];
        华: string[][];
        金: string[][];
        魏: string[][];
        陶: string[][];
        姜: string[][];
        戚: string[][];
        谢: string[][];
        邹: string[][];
        喻: string[][];
        柏: string[][];
        水: string[][];
        窦: string[][];
        章: string[][];
        云: string[][];
        苏: string[][];
        潘: string[][];
        葛: string[][];
        奚: string[][];
        范: string[][];
        彭: string[][];
        郎: string[][];
        鲁: string[][];
        韦: string[][];
        昌: string[][];
        马: string[][];
        苗: string[][];
        凤: string[][];
        花: string[][];
        方: string[][];
        俞: string[][];
        任: string[][];
        袁: string[][];
        柳: string[][];
        酆: string[][];
        鲍: string[][];
        史: string[][];
        唐: string[][];
        费: string[][];
        廉: string[][];
        岑: string[][];
        薛: string[][];
        雷: string[][];
        贺: string[][];
        倪: string[][];
        汤: string[][];
        滕: string[][];
        殷: string[][];
        罗: string[][];
        毕: string[][];
        郝: string[][];
        邬: string[][];
        安: string[][];
        常: string[][];
        乐: string[][];
        于: string[][];
        时: string[][];
        傅: string[][];
        皮: string[][];
        卞: string[][];
        齐: string[][];
        康: string[][];
        伍: string[][];
        余: string[][];
        元: string[][];
        卜: string[][];
        顾: string[][];
        孟: string[][];
        平: string[][];
        黄: string[][];
        和: string[][];
        穆: string[][];
        萧: string[][];
        尹: string[][];
        姚: string[][];
        邵: string[][];
        湛: string[][];
        汪: string[][];
        祁: string[][];
        毛: string[][];
        禹: string[][];
        狄: string[][];
        米: string[][];
        贝: string[][];
        明: string[][];
        臧: string[][];
        计: string[][];
        伏: string[][];
        成: string[][];
        戴: string[][];
        谈: string[][];
        宋: string[][];
        茅: string[][];
        庞: string[][];
        熊: string[][];
        纪: string[][];
        舒: string[][];
        屈: string[][];
        项: string[][];
        祝: string[][];
        董: string[][];
        梁: string[][];
        杜: string[][];
        阮: string[][];
        蓝: string[][];
        闵: string[][];
        席: string[][];
        季: string[][];
        麻: string[][];
        强: string[][];
        贾: string[][];
        路: string[][];
        娄: string[][];
        危: string[][];
        江: string[][];
        童: string[][];
        颜: string[][];
        郭: string[][];
        梅: string[][];
        盛: string[][];
        林: string[][];
        刁: string[][];
        钟: string[][];
        徐: string[][];
        邱: string[][];
        骆: string[][];
        高: string[][];
        夏: string[][];
        蔡: string[][];
        田: string[][];
        樊: string[][];
        胡: string[][];
        凌: string[][];
        霍: string[][];
        虞: string[][];
        万: string[][];
        支: string[][];
        柯: string[][];
        昝: string[][];
        管: string[][];
        卢: string[][];
        莫: string[][];
        经: string[][];
        房: string[][];
        裘: string[][];
        缪: string[][];
        干: string[][];
        解: string[][];
        应: string[][];
        宗: string[][];
        丁: string[][];
        宣: string[][];
        贲: string[][];
        邓: string[][];
        郁: string[][];
        单: string[][];
        杭: string[][];
        洪: string[][];
        包: string[][];
        诸: string[][];
        左: string[][];
        石: string[][];
        崔: string[][];
        吉: string[][];
        钮: string[][];
        龚: string[][];
        程: string[][];
        嵇: string[][];
        邢: string[][];
        滑: string[][];
        裴: string[][];
        陆: string[][];
        荣: string[][];
        翁: string[][];
        荀: string[][];
        羊: string[][];
        於: string[][];
        惠: string[][];
        甄: string[][];
        曲: string[][];
        家: string[][];
        封: string[][];
        芮: string[][];
        羿: string[][];
        储: string[][];
        靳: string[][];
        汲: string[][];
        邴: string[][];
        糜: string[][];
        松: string[][];
        井: string[][];
        段: string[][];
        富: string[][];
        巫: string[][];
        乌: string[][];
        焦: string[][];
        巴: string[][];
        弓: string[][];
        牧: string[][];
        隗: string[][];
        山: string[][];
        谷: string[][];
        车: string[][];
        侯: string[][];
        宓: string[][];
        蓬: string[][];
        全: string[][];
        郗: string[][];
        班: string[][];
        仰: string[][];
        秋: string[][];
        仲: string[][];
        伊: string[][];
        宫: string[][];
        宁: string[][];
        仇: string[][];
        栾: string[][];
        暴: string[][];
        甘: string[][];
        钭: string[][];
        厉: string[][];
        戎: string[][];
        祖: string[][];
        武: string[][];
        符: string[][];
        刘: string[][];
        景: string[][];
        詹: string[][];
        束: string[][];
        龙: string[][];
        叶: string[][];
        幸: string[][];
        司: string[][];
        韶: string[][];
        郜: string[][];
        黎: string[][];
        蓟: string[][];
        薄: string[][];
        印: string[][];
        宿: string[][];
        白: string[][];
        怀: string[][];
        蒲: string[][];
        邰: string[][];
        从: string[][];
        鄂: string[][];
        索: string[][];
        咸: string[][];
        籍: string[][];
        赖: string[][];
        卓: string[][];
        蔺: string[][];
        屠: string[][];
        蒙: string[][];
        池: string[][];
        乔: string[][];
        阴: string[][];
        鬱: string[][];
        胥: string[][];
        能: string[][];
        苍: string[][];
        双: string[][];
        闻: string[][];
        莘: string[][];
        党: string[][];
        翟: string[][];
        谭: string[][];
        贡: string[][];
        劳: string[][];
        逄: string[][];
        姬: string[][];
        申: string[][];
        扶: string[][];
        堵: string[][];
        冉: string[][];
        宰: string[][];
        郦: string[][];
        雍: string[][];
        郤: string[][];
        璩: string[][];
        桑: string[][];
        桂: string[][];
        濮: string[][];
        牛: string[][];
        寿: string[][];
        通: string[][];
        边: string[][];
        扈: string[][];
        燕: string[][];
        冀: string[][];
        郏: string[][];
        浦: string[][];
        尚: string[][];
        农: string[][];
        温: string[][];
        别: string[][];
        庄: string[][];
        晏: string[][];
        柴: string[][];
        瞿: string[][];
        阎: string[][];
        充: string[][];
        慕: string[][];
        连: string[][];
        茹: string[][];
        习: string[][];
        宦: string[][];
        艾: string[][];
        鱼: string[][];
        容: string[][];
        向: string[][];
        古: string[][];
        易: string[][];
        慎: string[][];
        戈: string[][];
        廖: string[][];
        庾: string[][];
        终: string[][];
        暨: string[][];
        居: string[][];
        衡: string[][];
        步: string[][];
        都: string[][];
        耿: string[][];
        满: string[][];
        弘: string[][];
        匡: string[][];
        国: string[][];
        文: string[][];
        寇: string[][];
        广: string[][];
        禄: string[][];
        阙: string[][];
        东: string[][];
        欧: string[][];
        殳: string[][];
        沃: string[][];
        利: string[][];
        蔚: string[][];
        越: string[][];
        夔: string[][];
        隆: string[][];
        师: string[][];
        巩: string[][];
        厍: string[][];
        聂: string[][];
        晁: string[][];
        勾: string[][];
        敖: string[][];
        融: string[][];
        冷: string[][];
        訾: string[][];
        辛: string[][];
        阚: string[][];
        那: string[][];
        简: string[][];
        饶: string[][];
        空: string[][];
        曾: string[][];
        母: string[][];
        沙: string[][];
        乜: string[][];
        养: string[][];
        鞠: string[][];
        须: string[][];
        丰: string[][];
        巢: string[][];
        关: string[][];
        蒯: string[][];
        相: string[][];
        查: string[][];
        后: string[][];
        荆: string[][];
        红: string[][];
        游: string[][];
        竺: string[][];
        权: string[][];
        逯: string[][];
        盖: string[][];
        益: string[][];
        桓: string[][];
        公: string[][];
        牟: string[][];
        哈: string[][];
        言: string[][];
        福: string[][];
    };
    export default _default;
}
declare module "data/compound_surname" {
    const _default_1: {
        万俟: string[][];
        上官: string[][];
        东方: string[][];
        东郭: string[][];
        东门: string[][];
        乐正: string[][];
        亓官: string[][];
        仉督: string[][];
        令狐: string[][];
        仲孙: string[][];
        公冶: string[][];
        公孙: string[][];
        公羊: string[][];
        公良: string[][];
        公西: string[][];
        单于: string[][];
        南宫: string[][];
        南门: string[][];
        司寇: string[][];
        司徒: string[][];
        司空: string[][];
        司马: string[][];
        呼延: string[][];
        壤驷: string[][];
        夏侯: string[][];
        太叔: string[][];
        夹谷: string[][];
        子车: string[][];
        宇文: string[][];
        宗政: string[][];
        宰父: string[][];
        尉迟: string[][];
        左丘: string[][];
        巫马: string[][];
        慕容: string[][];
        拓跋: string[][];
        梁丘: string[][];
        榖梁: string[][];
        欧阳: string[][];
        段干: string[][];
        淳于: string[][];
        漆雕: string[][];
        澹台: string[][];
        濮阳: string[][];
        申屠: string[][];
        百里: string[][];
        皇甫: string[][];
        端木: string[][];
        第五: string[][];
        羊舌: string[][];
        西门: string[][];
        诸葛: string[][];
        赫连: string[][];
        轩辕: string[][];
        钟离: string[][];
        长孙: string[][];
        闻人: string[][];
        闾丘: string[][];
        颛孙: string[][];
        鲜于: string[][];
    };
    export default _default_1;
}
declare module "PinyinBase" {
    import { ENUM_PINYIN_MODE, ENUM_PINYIN_STYLE } from "constant";
    import type { IPinyinAllOptions, IPinyinOptions, IPinyinSegment, IPinyin } from "declare";
    export default class PinyinBase {
        STYLE_TONE: ENUM_PINYIN_STYLE;
        STYLE_TONE2: ENUM_PINYIN_STYLE;
        STYLE_TO3NE: ENUM_PINYIN_STYLE;
        STYLE_NORMAL: ENUM_PINYIN_STYLE;
        STYLE_INITIALS: ENUM_PINYIN_STYLE;
        STYLE_FIRST_LETTER: ENUM_PINYIN_STYLE;
        STYLE_PASSPORT: ENUM_PINYIN_STYLE;
        MODE_NORMAL: ENUM_PINYIN_MODE;
        MODE_SURNAME: ENUM_PINYIN_MODE;
        /**
         * 拼音转换入口。
         */
        pinyin(hans: string, options?: IPinyinOptions): string[][];
        /**
         * 不使用分词算法的拼音转换。
         */
        normal_pinyin(hans: string, options: IPinyinAllOptions): string[][];
        /**
         * 单字拼音转换。
         * @param {String} han, 单个汉字
         * @return {Array} 返回拼音列表，多音字会有多个拼音项。
         */
        single_pinyin(han: string, options: IPinyinAllOptions): string[];
        segment(hans: string, segmentType?: IPinyinSegment): string[];
        /**
         * 将文本分词，并转换成拼音。
         */
        segment_pinyin(hans: string, options: IPinyinAllOptions): string[][];
        /**
         * 词语注音
         * @param {String} phrases, 指定的词组。
         * @param {Object} options, 选项。
         * @return {Array}
         */
        phrases_pinyin(phrases: string, options: IPinyinAllOptions): string[][];
        groupPhrases(phrases: string[][]): string[];
        surname_pinyin(hans: string, options: IPinyinAllOptions): string[][];
        compound_surname(hans: string, options: IPinyinAllOptions): string[][];
        single_surname(hans: string, options: IPinyinAllOptions): string[][];
        /**
         * 比较两个汉字转成拼音后的排序顺序，可以用作默认的拼音排序算法。
         *
         * @param {String} hanA 汉字字符串 A。
         * @return {String} hanB 汉字字符串 B。
         * @return {Number} 返回 -1，0，或 1。
         */
        compare(hanA: string, hanB: string): number;
        compact(pys: string[][]): string[][];
    }
    export function getPinyinInstance(py: PinyinBase): IPinyin;
}
declare module "phonetic-symbol" {
    const phonetic_symbol: Record<string, string>;
    export default phonetic_symbol;
}
declare module "pinyin-web" {
    import PinyinBase from "PinyinBase";
    export class Pinyin extends PinyinBase {
    }
    export const pinyin: import("declare").IPinyin;
    export default pinyin;
    export const compare: (a: string, b: string) => number;
    export { compact } from "util";
}
declare module "segment" {
    import type { IPinyinSegment } from "declare";
    /**
     * TODO: 分词并带词性信息，需要调整 segment_pinyin 方法。
     * 分词并标注词性。
     */
    export function segment(hans: string, segment?: IPinyinSegment): string[];
}
declare module "pinyin" {
    import PinyinBase from "PinyinBase";
    import type { IPinyinSegment } from "declare";
    export class Pinyin extends PinyinBase {
        segment(hans: string, segmentType?: IPinyinSegment): string[];
    }
    export const pinyin: import("declare").IPinyin;
    export default pinyin;
    export const compare: (a: string, b: string) => number;
    export { compact } from "util";
}
