{"name": "pinyin", "version": "3.1.0", "description": "汉语拼音转换工具。", "main": "./lib/pinyin.js", "module": "./esm/pinyin.js", "browser": {"./lib/pinyin.js": "./lib/pinyin-web.js", "./esm/pinyin.js": "./esm/pinyin-web.js"}, "bin": {"pinyin": "./bin/pinyin"}, "homepage": "https://pinyin.js.org/", "author": "闲耘 <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/hotoo/pinyin.git"}, "bugs": {"url": "https://github.com/hotoo/pinyin/issues"}, "scripts": {"start": "dumi dev", "prepublishOnly": "npm run build", "build": "tsc --downlevelIteration -p tsconfig-es5.json & tsc --downlevelIteration -p tsconfig.json & npm run build:amd", "build:amd": "tsc --downlevelIteration -p tsconfig-amd.json", "doc:build": "rm -rf ./src/.umi && dumi build", "doc:deploy": "npm run doc:build && cp CNAME docs-dist/dist && gh-pages -d docs-dist/dist", "lint": "eslint ./src/ ./test/", "test": "jest --coverage", "debug": "npx ts-node tools/debug.ts"}, "dependencies": {"commander": "~1.1.1"}, "optionalDependencies": {"@node-rs/jieba": "^1.6.0", "nodejieba": "2.5.2", "segmentit": "^2.0.3"}, "devDependencies": {"@babel/preset-env": "^7.15.6", "@babel/preset-typescript": "^7.15.0", "@types/jest": "^27.0.2", "@typescript-eslint/eslint-plugin": "^5.14.0", "@typescript-eslint/parser": "^5.14.0", "aurl": "^1.2.0", "aws-sdk": "^2.1011.0", "beautify-benchmark": "^0.2.4", "benchmark": "~1.0.0", "dumi": "^1.1.30", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-prettier": "^4.0.0", "gh-pages": "^3.2.3", "jest": "^27.2.3", "mock-aws-s3": "^4.0.2", "nock": "^13.1.4", "npx": "^10.2.2", "prettier": "^2.6.0", "react-json-view": "^1.21.3", "request": "~2.68.0", "ts-node": "^10.5.0", "typescript": "^4.4.4"}, "registry": "https://registry.npmmirror.com", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "files": ["package.json", "build/", "bin", "data", "lib", "esm", "amd", "index.js"], "keywords": ["拼音", "汉语", "汉字", "中文", "<PERSON><PERSON><PERSON>"], "directories": {"example": "examples", "test": "test"}, "engines": {"install-node": "^18.0.0"}, "license": "MIT"}