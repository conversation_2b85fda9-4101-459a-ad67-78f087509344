{"name": "pinyin-pro", "version": "3.26.0", "description": "准确率和性能最优异的汉字转拼音库。获取中文拼音、韵母、声母、声调、首字母，支持拼音匹配", "main": "./dist/index.js", "module": "./dist/index.mjs", "typings": "./types/index.d.ts", "scripts": {"test": "vitest run --coverage", "build": "rollup -c && rollup -c rollup.esm.config.js", "commit": "git-cz", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/zh-lx/pinyin-pro.git"}, "files": ["dist", "types"], "keywords": ["拼音", "pinyin", "汉字", "中文", "首字母", "韵母", "声母", "音调"], "author": "zh-lx", "license": "MIT", "bugs": {"url": "https://github.com/zh-lx/pinyin-pro/issues"}, "homepage": "https://pinyin-pro.cn", "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@pinyin-pro/data": "1.0.3", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "@vitest/coverage-v8": "^1.5.0", "babel-eslint": "^10.1.0", "commitizen": "^4.2.2", "eslint": "^7.22.0", "rollup": "2.60.0", "rollup-plugin-alias": "^2.2.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "typescript": "^4.2.3", "vitest": "^1.5.0"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "sideEffects": false}