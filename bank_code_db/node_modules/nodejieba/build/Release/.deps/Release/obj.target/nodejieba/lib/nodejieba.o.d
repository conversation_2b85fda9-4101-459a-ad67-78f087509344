cmd_Release/obj.target/nodejieba/lib/nodejieba.o := c++ -o Release/obj.target/nodejieba/lib/nodejieba.o ../lib/nodejieba.cpp '-DNODE_GYP_MODULE_NAME=nodejieba' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/src -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.14.0/deps/v8/include -I../../node-addon-api -I../deps  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=10.7 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -mmacosx-version-min=10.7 -std=c++11 -stdlib=libc++ -DLOGGING_LEVEL=LL_WARNING -MMD -MF ./Release/.deps/Release/obj.target/nodejieba/lib/nodejieba.o.d.raw   -c
Release/obj.target/nodejieba/lib/nodejieba.o: ../lib/nodejieba.cpp \
  ../lib/nodejieba.h ../../node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/node_api_types.h \
  ../../node-addon-api/napi-inl.h \
  ../../node-addon-api/napi-inl.deprecated.h ../lib/utils.h \
  ../deps/cppjieba/Jieba.hpp ../deps/cppjieba/QuerySegment.hpp \
  ../deps/limonp/Logging.hpp ../deps/cppjieba/DictTrie.hpp \
  ../deps/limonp/StringUtil.hpp ../deps/limonp/StdExtension.hpp \
  ../deps/cppjieba/Unicode.hpp ../deps/limonp/LocalVector.hpp \
  ../deps/cppjieba/Trie.hpp ../deps/cppjieba/SegmentBase.hpp \
  ../deps/cppjieba/PreFilter.hpp ../deps/cppjieba/FullSegment.hpp \
  ../deps/cppjieba/MixSegment.hpp ../deps/cppjieba/MPSegment.hpp \
  ../deps/cppjieba/SegmentTagged.hpp ../deps/cppjieba/PosTagger.hpp \
  ../deps/cppjieba/HMMSegment.hpp ../deps/cppjieba/HMMModel.hpp \
  ../deps/cppjieba/KeywordExtractor.hpp
../lib/nodejieba.cpp:
../lib/nodejieba.h:
../../node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/22.14.0/include/node/node_api_types.h:
../../node-addon-api/napi-inl.h:
../../node-addon-api/napi-inl.deprecated.h:
../lib/utils.h:
../deps/cppjieba/Jieba.hpp:
../deps/cppjieba/QuerySegment.hpp:
../deps/limonp/Logging.hpp:
../deps/cppjieba/DictTrie.hpp:
../deps/limonp/StringUtil.hpp:
../deps/limonp/StdExtension.hpp:
../deps/cppjieba/Unicode.hpp:
../deps/limonp/LocalVector.hpp:
../deps/cppjieba/Trie.hpp:
../deps/cppjieba/SegmentBase.hpp:
../deps/cppjieba/PreFilter.hpp:
../deps/cppjieba/FullSegment.hpp:
../deps/cppjieba/MixSegment.hpp:
../deps/cppjieba/MPSegment.hpp:
../deps/cppjieba/SegmentTagged.hpp:
../deps/cppjieba/PosTagger.hpp:
../deps/cppjieba/HMMSegment.hpp:
../deps/cppjieba/HMMModel.hpp:
../deps/cppjieba/KeywordExtractor.hpp:
