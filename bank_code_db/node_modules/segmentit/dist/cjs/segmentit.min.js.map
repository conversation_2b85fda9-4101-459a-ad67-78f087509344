{"version": 3, "file": "segmentit.min.js", "sources": ["../../src/POSTAG.js", "../../src/Tokenizer.js", "../../src/Optimizer.js", "../../src/Segment.js", "../../src/module/BaseModule.js", "../../src/module/COLORS.js", "../../src/module/AdjectiveOptimizer.js", "../../src/module/CHS_NAMES.js", "../../src/module/ChsNameTokenizer.js", "../../src/module/DictOptimizer.js", "../../src/module/EmailOptimizer.js", "../../src/module/PunctuationTokenizer.js", "../../src/module/URLTokenizer.js", "../../src/module/ChsNameOptimizer.js", "../../src/module/DatetimeOptimizer.js", "../../src/module/DictTokenizer.js", "../../src/module/ForeignTokenizer.js", "../../src/module/SingleTokenizer.js", "../../src/module/WildcardTokenizer.js", "../../src/module/index.js", "../../src/knowledge/index.js", "../../src/index.js"], "sourcesContent": ["// @flow\n\n/**\n * 单词类型\n */\n\nconst POSTAG = {\n  D_A: 0x40000000, // 形容词 形语素\n  D_B: 0x20000000, // 区别词 区别语素\n  D_C: 0x10000000, // 连词 连语素\n  D_D: 0x08000000, // 副词 副语素\n  D_E: 0x04000000, // 叹词 叹语素\n  D_F: 0x02000000, // 方位词 方位语素\n  D_I: 0x01000000, // 成语\n  D_L: 0x00800000, // 习语\n  A_M: 0x00400000, // 数词 数语素\n  D_MQ: 0x00200000, // 数量词\n  D_N: 0x00100000, // 名词 名语素\n  D_O: 0x00080000, // 拟声词\n  D_P: 0x00040000, // 介词\n  A_Q: 0x00020000, // 量词 量语素\n  D_R: 0x00010000, // 代词 代语素\n  D_S: 0x00008000, // 处所词\n  D_T: 0x00004000, // 时间词\n  D_U: 0x00002000, // 助词 助语素\n  D_V: 0x00001000, // 动词 动语素\n  D_W: 0x00000800, // 标点符号\n  D_X: 0x00000400, // 非语素字\n  D_Y: 0x00000200, // 语气词 语气语素\n  D_Z: 0x00000100, // 状态词\n  A_NR: 0x00000080, // 人名\n  A_NS: 0x00000040, // 地名\n  A_NT: 0x00000020, // 机构团体\n  A_NX: 0x00000010, // 外文字符\n  A_NZ: 0x00000008, // 其他专名\n  D_ZH: 0x00000004, // 前接成分\n  D_K: 0x00000002, // 后接成分\n  UNK: 0x00000000, // 未知词性\n  URL: 0x00000001, // 网址、邮箱地址\n};\n\nconst CN_POS_NAMES = {\n  D_A: '形容词 形语素',\n  D_B: '区别词 区别语素',\n  D_C: '连词 连语素',\n  D_D: '副词 副语素',\n  D_E: '叹词 叹语素',\n  D_F: '方位词 方位语素',\n  D_I: '成语',\n  D_L: '习语',\n  A_M: '数词 数语素',\n  D_MQ: '数量词',\n  D_N: '名词 名语素',\n  D_O: '拟声词',\n  D_P: '介词',\n  A_Q: '量词 量语素',\n  D_R: '代词 代语素',\n  D_S: '处所词',\n  D_T: '时间词',\n  D_U: '助词 助语素',\n  D_V: '动词 动语素',\n  D_W: '标点符号',\n  D_X: '非语素字',\n  D_Y: '语气词 语气语素',\n  D_Z: '状态词',\n  A_NR: '人名',\n  A_NS: '地名',\n  A_NT: '机构团体',\n  A_NX: '外文字符',\n  A_NZ: '其他专名',\n  D_ZH: '前接成分',\n  D_K: '后接成分',\n  UNK: '未知',\n  URL: '网址 邮箱地址',\n};\n\nconst EN_POS_NAMES = {\n  D_A: 'a',\n  D_B: 'b',\n  D_C: 'c',\n  D_D: 'd',\n  D_E: 'e',\n  D_F: 'f',\n  D_I: 'i',\n  D_L: 'l',\n  A_M: 'm',\n  D_MQ: 'mq',\n  D_N: 'n',\n  D_O: 'o',\n  D_P: 'p',\n  A_Q: 'q',\n  D_R: 'r',\n  D_S: 's',\n  D_T: 't',\n  D_U: 'u',\n  D_V: 'v',\n  D_W: 'w',\n  D_X: 'x',\n  D_Y: 'y',\n  D_Z: 'z',\n  A_NR: 'nr',\n  A_NS: 'ns',\n  A_NT: 'nt',\n  A_NX: 'nx',\n  A_NZ: 'nz',\n  D_ZH: 'h',\n  D_K: 'k',\n  UNK: 'un',\n  URL: 'uri',\n};\n\nexport function getPOSTagTranslator(POSTagDict: Object, I18NDict: Object): string {\n  return (posTagNumber: number): string => {\n    if (isNaN(posTagNumber)) {\n      return I18NDict[posTagNumber] || I18NDict.UNK;\n    }\n    const result = [];\n    for (const key in POSTagDict) {\n      if ((posTagNumber & POSTagDict[key]) > 0) {\n        result.push(I18NDict[key]);\n      }\n    }\n    if (result.length < 1) {\n      return I18NDict.UNK;\n    }\n    return result.toString();\n  };\n}\n\nexport const cnPOSTag = getPOSTagTranslator(POSTAG, CN_POS_NAMES);\nexport const enPOSTag = getPOSTagTranslator(POSTAG, EN_POS_NAMES);\n\nexport default POSTAG;\n", "// @flow\nimport type { SegmentToken } from './module/type';\n\nexport default class Tokenizer {\n  /**\n   * 分词模块管理器\n   *\n   * @param {Segment} 分词接口\n   */\n  constructor(segment) {\n    this.segment = segment;\n  }\n\n  /**\n   * 对一段文本进行分词\n   *\n   * @param {string} text 文本\n   * @param {array} modules 分词模块数组\n   * @return {array}\n   */\n  split(text, modules): SegmentToken[] {\n    if (modules.length < 1) {\n      throw Error('No tokenizer module!');\n    }\n    // 按顺序分别调用各个module来进行分词 ： 各个module仅对没有识别类型的单词进行分词\n    let result = [{ w: text }];\n    modules.forEach(module => {\n      result = module.split(result);\n    });\n    return result;\n  }\n}\n", "// @flow\nimport type { SegmentToken } from './module/type';\n\nexport default class Optimizer {\n  /**\n   * 优化模块管理器\n   *\n   * @param {Segment} 分词接口\n   */\n  constructor(segment) {\n    this.segment = segment;\n  }\n\n  /**\n   * 分词一段文本\n   *\n   * @param {array} words 单词数组\n   * @param {array} modules 分词模块数组\n   * @return {array}\n   */\n  doOptimize(words, modules): SegmentToken[] {\n    let result = [...words];\n    // 按顺序分别调用各个module来进行分词，各个module仅对没有识别类型的单词进行分词\n    modules.forEach(module => {\n      result = module.doOptimize(result);\n    });\n    return result;\n  }\n}\n", "// @flow\n/**\n * 分词器接口\n *\n * <AUTHOR>\n * <AUTHOR>\n */\n\nimport POSTAG from './POSTAG';\nimport Tokenizer from './Tokenizer';\nimport Optimizer from './Optimizer';\n\n/**\n * 创建分词器接口\n */\nexport default class Segment {\n  constructor() {\n    this.POSTAG = POSTAG; // 词性\n    this.DICT = {}; // 词典表\n    this.modules = {\n      tokenizer: [], // 分词模块\n      optimizer: [], // 优化模块\n    };\n    this.tokenizer = new Tokenizer(this);\n    this.optimizer = new Optimizer(this);\n  }\n\n  /**\n   * 载入分词模块\n   *\n   * @param {String|Array|Object} module 模块名称(数组)或模块对象\n   * @return {Segment}\n   */\n  use = (Module: Array | Object): Segment => {\n    // 传入列表的话就递归调用\n    if (Array.isArray(Module)) {\n      Module.forEach(this.use);\n    } else {\n      // 初始化并注册模块\n      if (typeof Module.init === 'function') {\n        Module.init(this);\n        this.modules[Module.type].push(Module);\n      } else {\n        const module = new Module(this);\n        this.modules[module.type].push(module);\n      }\n    }\n\n    return this;\n  };\n\n  /**\n   * 载入字典文件\n   *\n   * @param {String} name 字典文件名\n   * @param {String} type 类型\n   * @param {Boolean} convertToLower 是否全部转换为小写\n   * @return {Segment}\n   */\n  loadDict = (\n    dict: string | string[],\n    type = 'TABLE',\n    convertToLower: boolean = false,\n  ): Segment => {\n    if (Array.isArray(dict)) {\n      dict.forEach(d => this.loadDict(d));\n    } else {\n      // 初始化词典\n      if (!this.DICT[type]) this.DICT[type] = {};\n      if (!this.DICT[`${type}2`]) this.DICT[`${type}2`] = {};\n      const TABLE = this.DICT[type]; // 词典表  '词' => {属性}\n      const TABLE2 = this.DICT[`${type}2`]; // 词典表  '长度' => '词' => 属性\n      // 导入数据\n      dict\n        .split(/\\r?\\n/)\n        .map(line => {\n          if (convertToLower) return line.toLowerCase();\n          return line;\n        })\n        .forEach(line => {\n          const blocks = line.split('|');\n          if (blocks.length > 2) {\n            const w = blocks[0].trim();\n            const p = Number(blocks[1]);\n            const f = Number(blocks[2]);\n\n            // 一定要检查单词是否为空，如果为空会导致Bug\n            if (w.length > 0) {\n              TABLE[w] = { f, p };\n              if (!TABLE2[w.length]) TABLE2[w.length] = {};\n              TABLE2[w.length][w] = TABLE[w];\n            }\n          }\n        });\n    }\n\n    return this;\n  };\n\n  /**\n   * 取词典表\n   *\n   * @param {String} type 类型\n   * @return {object}\n   */\n  getDict = (type: string) => this.DICT[type];\n\n  /**\n   * 载入同义词词典\n   *\n   * @param {Object} dict 字典文件\n   */\n  loadSynonymDict = (dict: string | string[]): Segment => {\n    if (Array.isArray(dict)) {\n      dict.forEach(d => this.loadSynonymDict(d));\n    } else {\n      const type = 'SYNONYM';\n\n      // 初始化词典\n      if (!this.DICT[type]) this.DICT[type] = {};\n      const TABLE = this.DICT[type]; // 词典表  '同义词' => '标准词'\n      // 导入数据\n      dict\n        .split(/\\r?\\n/)\n        .map(line => line.split(','))\n        .forEach(blocks => {\n          if (blocks.length > 1) {\n            const n1 = blocks[0].trim();\n            const n2 = blocks[1].trim();\n            TABLE[n1] = n2;\n            if (TABLE[n2] === n1) {\n              delete TABLE[n2];\n            }\n          }\n        });\n    }\n\n    return this;\n  };\n\n  /**\n   * 载入停止符词典\n   *\n   * @param {Object} dict 字典文件\n   */\n  loadStopwordDict = (dict: string | string[]): Segment => {\n    if (Array.isArray(dict)) {\n      dict.forEach(d => this.loadStopwordDict(d));\n    } else {\n      const type = 'STOPWORD';\n\n      // 初始化词典\n      if (!this.DICT[type]) this.DICT[type] = {};\n      const TABLE = this.DICT[type]; // 词典表  '同义词' => '标准词'\n      // 导入数据\n      dict\n        .split(/\\r?\\n/)\n        .map(line => line.trim())\n        .forEach(line => {\n          if (line) {\n            TABLE[line] = true;\n          }\n        });\n    }\n\n    return this;\n  };\n\n  /**\n   * 开始分词\n   *\n   * @param {String} text 文本\n   * @param {Object} options 选项\n   *   - {Boolean} simple 是否仅返回单词内容\n   *   - {Boolean} stripPunctuation 去除标点符号\n   *   - {Boolean} convertSynonym 转换同义词\n   *   - {Boolean} stripStopword 去除停止符\n   * @return {Array}\n   */\n  doSegment = (text, options) => {\n    const me = this;\n    options = options || {};\n    let ret = [];\n\n    // 将文本按照换行符分割成多段，并逐一分词\n    text\n      .replace(/\\r/g, '\\n')\n      // 用换行符和空格把长文本切小，以减小传入中间件的数组长度\n      .split(/\\n+/)\n      .forEach(section => {\n        var section = section.trim();\n        if (section.length < 1) return;\n        // ======================================\n        // 分词\n        let sret = me.tokenizer.split(section, me.modules.tokenizer);\n\n        // 优化\n        sret = me.optimizer.doOptimize(sret, me.modules.optimizer);\n\n        // ======================================\n        // 连接分词结果\n        if (sret.length > 0) ret = ret.concat(sret);\n      });\n\n    // 去除标点符号\n    if (options.stripPunctuation) {\n      ret = ret.filter(item => item.p !== POSTAG.D_W);\n    }\n\n    // 转换同义词\n    function convertSynonym(list) {\n      let count = 0;\n      const TABLE = me.getDict('SYNONYM');\n      list = list.map(item => {\n        if (item.w in TABLE) {\n          count++;\n          return { w: TABLE[item.w], p: item.p };\n        }\n        return item;\n      });\n      return { count, list };\n    }\n    if (options.convertSynonym) {\n      do {\n        var result = convertSynonym(ret);\n        ret = result.list;\n      } while (result.count > 0);\n    }\n\n    // 去除停止符\n    if (options.stripStopword) {\n      const STOPWORD = me.getDict('STOPWORD');\n      ret = ret.filter(item => !(item.w in STOPWORD));\n    }\n\n    // 仅返回单词内容\n    if (options.simple) {\n      ret = ret.map(item => item.w);\n    }\n\n    return ret;\n  };\n\n  /**\n   * 将单词数组连接成字符串\n   *\n   * @param {Array} words 单词数组\n   * @return {String}\n   */\n  toString(words) {\n    return words.map(item => item.w).join('');\n  }\n\n  /**\n   * 根据某个单词或词性来分割单词数组\n   *\n   * @param {Array} words 单词数组\n   * @param {Number|String} s 用于分割的单词或词性\n   * @return {Array}\n   */\n  split(words, s) {\n    const ret = [];\n    let lasti = 0;\n    let i = 0;\n    const f = typeof s === 'string' ? 'w' : 'p';\n\n    while (i < words.length) {\n      if (words[i][f] === s) {\n        if (lasti < i) ret.push(words.slice(lasti, i));\n        ret.push(words.slice(i, i + 1));\n        i++;\n        lasti = i;\n      } else {\n        i++;\n      }\n    }\n    if (lasti < words.length - 1) {\n      ret.push(words.slice(lasti, words.length));\n    }\n\n    return ret;\n  }\n\n  /**\n   * 在单词数组中查找某一个单词或词性所在的位置\n   *\n   * @param {Array} words 单词数组\n   * @param {Number|String} s 要查找的单词或词性\n   * @param {Number} cur 开始位置\n   * @return {Number} 找不到，返回-1\n   */\n  indexOf(words, s, cur) {\n    cur = isNaN(cur) ? 0 : cur;\n    const f = typeof s === 'string' ? 'w' : 'p';\n\n    while (cur < words.length) {\n      if (words[cur][f] === s) return cur;\n      cur++;\n    }\n\n    return -1;\n  }\n}\n", "// @flow\nimport Segment from '../Segment';\n\nexport class Module {\n  type: 'optimizer' | 'tokenizer';\n\n  constructor(segment: Segment) {\n    this.segment = segment;\n  }\n}\n\nexport class Tokenizer extends Module {\n  type = 'tokenizer';\n}\n\nexport class Optimizer extends Module {\n  type = 'optimizer';\n}\n", "// @flow\n\nexport const COLOR_WITH_RGB: string[][] = [\n  ['薰衣草紫红', '#fff0f5', '255,245,245'],\n  ['淡藕合', '#f0dfee', '240,238,238'],\n  ['甘石粉', '#ffe8f3', '255,243,243'],\n  ['浅粉红', '#ffd9e6', '255,230,230'],\n  ['浅血牙', '#ffd7ea', '255,234,234'],\n  ['粉凤仙', '#fccce6', '252,230,230'],\n  ['浅珍珠红', '#ffb3e6', '255,230,230'],\n  ['淡粉', '#ffb3cc', '255,204,204'],\n  ['火鹤红', '#e68ab8', '230,184,184'],\n  ['豆棕', '#502e3e', '80,62,62'],\n  ['茄皮紫', '#3a212f', '58,47,47'],\n  ['蒲萄灰', '#4e2c3f', '78,63,63'],\n  ['荷花', '#eb7faf', '235,175,175'],\n  ['灰紫红', '#db7093', '219,147,147'],\n  ['浅珊瑚红', '#ff80bf', '255,191,191'],\n  ['暗粉', '#ff77bb', '255,187,187'],\n  ['陈玫红', '#b85798', '184,152,152'],\n  ['肉红', '#d26091', '210,145,145'],\n  ['尖晶石红', '#ff73b3', '255,179,179'],\n  ['银红', '#c85b92', '200,146,146'],\n  ['暖粉红', '#ff69b4', '255,180,180'],\n  ['浅玫瑰红', '#ff66cc', '255,204,204'],\n  ['紫扇贝色', '#923a60', '146,96,96'],\n  ['长春花色', '#ff47d1', '255,209,209'],\n  ['山茶红', '#e63995', '230,149,149'],\n  ['中青紫红', '#c71585', '199,133,133'],\n  ['深粉红', '#ff1493', '255,147,147'],\n  ['洋玫瑰红', '#ff0da6', '255,166,166'],\n  ['玫瑰红', '#ff007f', '255,127,127'],\n  ['品红', '#f400a1', '244,161,161'],\n  ['胭脂红', '#e6005c', '230,92,92'],\n  ['艶粉', '#e6005c', '230,92,92'],\n  ['红宝石色', '#cc0080', '204,128,128'],\n  ['枢机红', '#990036', '153,54,54'],\n  ['毅红', '#8a002e', '138,46,46'],\n  ['牵牛紫', '#800040', '128,64,64'],\n  ['紫粉', '#80002b', '128,43,43'],\n  ['鶏冠花红', '#660033', '102,51,51'],\n  ['枣红', '#640021', '100,33,33'],\n  ['紫绛色', '#51001b', '81,27,27'],\n  ['玫瑰灰', '#4d0026', '77,38,38'],\n  ['勃艮第酒红', '#470024', '71,36,36'],\n  ['黒紫', '#3e0014', '62,20,20'],\n  ['雪紫', '#33001a', '51,26,26'],\n  ['深红', '#33001a', '51,26,26'],\n  ['雪色', '#fffafa', '255,250,250'],\n  ['浅肉粉', '#f5eeeb', '245,235,235'],\n  ['粉棕', '#ebdad3', '235,211,211'],\n  ['雾玫瑰色', '#ffe4e1', '255,225,225'],\n  ['米红', '#fddfdf', '253,223,223'],\n  ['沙棕', '#e6c3c3', '230,195,195'],\n  ['肉粉', '#ffd7d7', '255,215,215'],\n  ['奶棕', '#deb6b6', '222,182,182'],\n  ['玫瑰褐', '#bc8f8f', '188,143,143'],\n  ['红灰莲', '#ab8282', '171,130,130'],\n  ['粉红', '#ffc0cb', '255,203,203'],\n  ['亮粉红', '#ffb6c1', '255,193,193'],\n  ['壳黄红', '#ffb3bf', '255,191,191'],\n  ['浅粉', '#ffb3b3', '255,179,179'],\n  ['十様锦', '#ffb3b3', '255,179,179'],\n  ['深烟', '#311f1e', '49,30,30'],\n  ['凤仙粉', '#ff9393', '255,147,147'],\n  ['亮珊瑚色', '#f08080', '240,128,128'],\n  ['暗鲑红', '#e9967a', '233,122,122'],\n  ['绛紫', '#492525', '73,37,37'],\n  ['浅鲑红', '#ff8099', '255,153,153'],\n  ['紫铜色', '#6b3636', '107,54,54'],\n  ['珈琲色', '#623131', '98,49,49'],\n  ['烟栗色', '#221311', '34,17,17'],\n  ['亮鲑红', '#ffa07a', '255,122,122'],\n  ['红莲灰', '#c76079', '199,121,121'],\n  ['鲑红', '#fa8072', '250,114,114'],\n  ['印度红', '#cd5c5c', '205,92,92'],\n  ['银朱', '#c85554', '200,84,84'],\n  ['赦红', '#d1634b', '209,75,75'],\n  ['二硃', '#c24545', '194,69,69'],\n  ['芯木色', '#c14444', '193,68,68'],\n  ['銹红', '#4d1919', '77,25,25'],\n  ['珊瑚红', '#ff7f50', '255,80,80'],\n  ['辰沙', '#ce3b3b', '206,59,59'],\n  ['红扇贝色', '#ce3b3b', '206,59,59'],\n  ['蕃茄红', '#ff6347', '255,71,71'],\n  ['柿子橙', '#ff4d40', '255,64,64'],\n  ['妃红', '#b8542e', '184,46,46'],\n  ['甎红', '#ab2b2b', '171,43,43'],\n  ['褐色', '#a52a2a', '165,42,42'],\n  ['硃砂', '#972626', '151,38,38'],\n  ['土红', '#bc2e2e', '188,46,46'],\n  ['樱桃红', '#de3163', '222,99,99'],\n  ['砖红色', '#b22222', '178,34,34'],\n  ['茜红', '#e32636', '227,54,54'],\n  ['银星海棠', '#f52443', '245,67,67'],\n  ['橙色', '#fe621f', '254,31,31'],\n  ['桔红', '#ff441a', '255,26,26'],\n  ['柿子色', '#ff531a', '255,26,26'],\n  ['绯红', '#dc143c', '220,60,60'],\n  ['月季红', '#e10b2b', '225,43,43'],\n  ['鸟罗松红', '#c80926', '200,38,38'],\n  ['蕉红色', '#d70419', '215,25,25'],\n  ['艶红', '#bf0417', '191,23,23'],\n  ['烟色', '#2e0a01', '46,1,1'],\n  ['牡丹红', '#b80233', '184,51,51'],\n  ['榴花红', '#b02502', '176,2,2'],\n  ['枣紫', '#540101', '84,1,1'],\n  ['红色', '#ff0000', '255,0,0'],\n  ['腥红', '#ff2400', '255,0,0'],\n  ['橙红', '#ff4500', '255,0,0'],\n  ['朱红', '#ff4d00', '255,0,0'],\n  ['鲜红', '#e60000', '230,0,0'],\n  ['曙红', '#e60039', '230,57,57'],\n  ['章丹', '#dd3700', '221,0,0'],\n  ['茉莉花红', '#cc0033', '204,51,51'],\n  ['猩红', '#cc0000', '204,0,0'],\n  ['象牙红', '#b30000', '179,0,0'],\n  ['血红', '#990000', '153,0,0'],\n  ['深釉红', '#971b00', '151,0,0'],\n  ['暗红', '#8b0000', '139,0,0'],\n  ['栗色', '#800000', '128,0,0'],\n  ['釉红', '#7b1800', '123,0,0'],\n  ['臙脂', '#700000', '112,0,0'],\n  ['花卉白', '#fffaf0', '255,240,240'],\n  ['海贝色', '#fff5ee', '255,238,238'],\n  ['浅棕灰', '#eee7dd', '238,221,221'],\n  ['亚麻色', '#faf0e6', '250,230,230'],\n  ['胡粉', '#fffae8', '255,232,232'],\n  ['旧蕾丝色', '#fdf5e6', '253,230,230'],\n  ['银白色', '#fffae6', '255,230,230'],\n  ['铁灰色', '#625b57', '98,87,87'],\n  ['蝋白', '#fef8de', '254,222,222'],\n  ['玉米丝色', '#fff8dc', '255,220,220'],\n  ['古董白', '#faebd7', '250,215,215'],\n  ['红梍', '#3a3932', '58,50,50'],\n  ['蕃木瓜色', '#ffefd5', '255,213,213'],\n  ['芽灰', '#e3dbbf', '227,191,191'],\n  ['米灰', '#d3cbaf', '211,175,175'],\n  ['元青', '#24231e', '36,30,30'],\n  ['果肉色', '#fee3d1', '254,209,209'],\n  ['杏仁白', '#ffebcd', '255,205,205'],\n  ['柠檬绸色', '#fffacd', '255,205,205'],\n  ['乳白色', '#fef3c9', '254,201,201'],\n  ['淡黄', '#fef8c9', '254,201,201'],\n  ['真丝紬色', '#ffe3c8', '255,200,200'],\n  ['肉黄', '#fce2c4', '252,196,196'],\n  ['芦黄', '#ebd2b8', '235,184,184'],\n  ['陶坯黄', '#ffe4c4', '255,196,196'],\n  ['深银灰', '#c7bd9a', '199,154,154'],\n  ['承徳梍', '#474336', '71,54,54'],\n  ['粉扑桃色', '#ffdab9', '255,185,185'],\n  ['小麦色', '#f5deb3', '245,179,179'],\n  ['乳黄色', '#feefb6', '254,182,182'],\n  ['小鸠黄', '#efe7ab', '239,171,171'],\n  ['鹿皮鞋色', '#ffe4b5', '255,181,181'],\n  ['桃色', '#ffe5b4', '255,180,180'],\n  ['牙黄', '#fef4b4', '254,180,180'],\n  ['富春纺色', '#fef4b4', '254,180,180'],\n  ['乳灰', '#cfc592', '207,146,146'],\n  ['灰米', '#c4be8c', '196,140,140'],\n  ['庭院瓦灰色', '#706750', '112,80,80'],\n  ['肉棕', '#debf9a', '222,154,154'],\n  ['灰土色', '#ccb38c', '204,140,140'],\n  ['那瓦霍白', '#ffdead', '255,173,173'],\n  ['甘草黄', '#eddd9e', '237,158,158'],\n  ['日晒色', '#d2b48c', '210,140,140'],\n  ['纸棕', '#d2b38c', '210,140,140'],\n  ['相思灰', '#4a4331', '74,49,49'],\n  ['蝶粉', '#ffd0a2', '255,162,162'],\n  ['乳棕', '#c9b481', '201,129,129'],\n  ['燋茶色', '#3d3a27', '61,39,39'],\n  ['浅驼色', '#d7af88', '215,136,136'],\n  ['中驼', '#ad876b', '173,107,107'],\n  ['玛瑙粉', '#ffbc9b', '255,155,155'],\n  ['硬木色', '#deb887', '222,135,135'],\n  ['土布色', '#fdd599', '253,153,153'],\n  ['骆驼色', '#dcb183', '220,131,131'],\n  ['亮卡其色', '#f0e68c', '240,140,140'],\n  ['中棕灰', '#695f3d', '105,61,61'],\n  ['淡棕茶', '#c0a86d', '192,109,109'],\n  ['草灰色', '#3d3123', '61,35,35'],\n  ['肉棕灰', '#cd9b69', '205,105,105'],\n  ['珈琲棕', '#705438', '112,56,56'],\n  ['深栗色', '#402f20', '64,32,32'],\n  ['深褐色', '#251c12', '37,18,18'],\n  ['黄棕色', '#b39255', '179,85,85'],\n  ['杏黄', '#e69966', '230,102,102'],\n  ['獣皮色', '#c69c57', '198,87,87'],\n  ['驼色', '#a16b47', '161,71,71'],\n  ['卡其黄', '#cda456', '205,86,86'],\n  ['蜜橙', '#ffb366', '255,102,102'],\n  ['茉莉黄', '#e6c35c', '230,92,92'],\n  ['红孤色', '#c98150', '201,80,80'],\n  ['沙褐', '#f4a460', '244,96,96'],\n  ['蛤蜊粉', '#ffb061', '255,97,97'],\n  ['丹东石', '#f9e459', '249,89,89'],\n  ['南瓜黄', '#f4ad57', '244,87,87'],\n  ['浅黄', '#f3aa58', '243,88,88'],\n  ['鹿皮色', '#fad156', '250,86,86'],\n  ['赦石色', '#d17547', '209,71,71'],\n  ['秘鲁色', '#cd853f', '205,63,63'],\n  ['向日黄', '#ffc34d', '255,77,77'],\n  ['将校呢', '#363210', '54,16,16'],\n  ['棕茶', '#c78738', '199,56,56'],\n  ['铜色', '#b87333', '184,51,51'],\n  ['赭黄', '#a0522d', '160,45,45'],\n  ['桂皮色', '#c66f35', '198,53,53'],\n  ['罗汉果色', '#502f16', '80,22,22'],\n  ['醤色', '#39200f', '57,15,15'],\n  ['雄黄', '#c66231', '198,49,49'],\n  ['金黄', '#b85c2e', '184,46,46'],\n  ['松皮色', '#a45128', '164,40,40'],\n  ['棕红', '#934824', '147,36,36'],\n  ['黄鸭色', '#874521', '135,33,33'],\n  ['浅桔黄', '#ec9433', '236,51,51'],\n  ['热带橙', '#ff8033', '255,51,51'],\n  ['卡其色', '#996b1f', '153,31,31'],\n  ['乌贼墨色', '#704214', '112,20,20'],\n  ['赭色', '#cc7722', '204,34,34'],\n  ['明黄', '#ffc428', '255,40,40'],\n  ['丝爪花黄', '#fed927', '254,39,39'],\n  ['大赤金', '#e6b422', '230,34,34'],\n  ['金菊色', '#daa520', '218,32,32'],\n  ['粉橙色', '#ff7b24', '255,36,36'],\n  ['巧克力色', '#d2691e', '210,30,30'],\n  ['鞍褐', '#8b4513', '139,19,19'],\n  ['选金', '#e9c61f', '233,31,31'],\n  ['库金', '#e0b712', '224,18,18'],\n  ['玛瑙色', '#b1470e', '177,14,14'],\n  ['黄金色', '#dab710', '218,16,16'],\n  ['琉璃色', '#c9780c', '201,12,12'],\n  ['暗金菊色', '#b8860b', '184,11,11'],\n  ['坛香色', '#d57306', '213,6,6'],\n  ['苍黄', '#c65306', '198,6,6'],\n  ['艶黄', '#fc8705', '252,5,5'],\n  ['雄精', '#d94f04', '217,4,4'],\n  ['姜黄', '#806102', '128,2,2'],\n  ['朱古力色', '#381801', '56,1,1'],\n  ['蛋黄', '#e6a202', '230,2,2'],\n  ['黄琉璃', '#e4b803', '228,3,3'],\n  ['枝黄', '#cfb603', '207,3,3'],\n  ['鹿皮棕', '#a67902', '166,2,2'],\n  ['阳橙', '#ff7300', '255,0,0'],\n  ['暗橙', '#ff8c00', '255,0,0'],\n  ['万寿菊黄', '#ff9900', '255,0,0'],\n  ['琥珀色', '#ffbf00', '255,0,0'],\n  ['金色', '#ffd700', '255,0,0'],\n  ['石黄', '#f9bb00', '249,0,0'],\n  ['鹅黄', '#f8b500', '248,0,0'],\n  ['橘色', '#f28500', '242,0,0'],\n  ['桔黄', '#f08300', '240,0,0'],\n  ['硃膘', '#eb6101', '235,1,1'],\n  ['土黄', '#e88b00', '232,0,0'],\n  ['铬黄', '#e6b800', '230,0,0'],\n  ['金红', '#e65c00', '230,0,0'],\n  ['虾黄', '#db6d00', '219,0,0'],\n  ['燃橙', '#cc5500', '204,0,0'],\n  ['椰褐', '#4d1f00', '77,0,0'],\n  ['咖啡色', '#4d3900', '77,0,0'],\n  ['灯草灰', '#131100', '19,0,0'],\n  ['蛤粉', '#fdfff4', '253,244,244'],\n  ['红灰', '#5f605b', '95,91,91'],\n  ['象牙色', '#fffff0', '255,240,240'],\n  ['貂水灰', '#8b8e86', '139,134,134'],\n  ['蝋黄', '#edf9df', '237,223,223'],\n  ['米黄色', '#f5f5dc', '245,220,220'],\n  ['松緑色', '#ced5bd', '206,189,189'],\n  ['黄灰', '#b6bea9', '182,169,169'],\n  ['亮黄', '#ffffe0', '255,224,224'],\n  ['春緑', '#e3efd1', '227,209,209'],\n  ['承徳灰', '#4f4f44', '79,68,68'],\n  ['油烟墨', '#272923', '39,35,35'],\n  ['淡米色', '#ffffd7', '255,215,215'],\n  ['亮金菊黄', '#fafad2', '250,210,210'],\n  ['芦灰', '#c1d0ae', '193,174,174'],\n  ['奶緑', '#cadcb6', '202,182,182'],\n  ['奶油色', '#fffdd0', '255,208,208'],\n  ['水黄', '#ddeec4', '221,196,196'],\n  ['銹緑', '#d9e7ba', '217,186,186'],\n  ['虾灰', '#dad6ab', '218,171,171'],\n  ['法国梧桐皮色', '#949773', '148,115,115'],\n  ['灰金菊色', '#eee8aa', '238,170,170'],\n  ['中条虾緑', '#687452', '104,82,82'],\n  ['茶緑', '#343724', '52,36,36'],\n  ['牙緑', '#f4fea3', '244,163,163'],\n  ['鉄緑', '#3f472c', '63,44,44'],\n  ['香槟黄', '#ffff99', '255,153,153'],\n  ['浅草緑', '#ccff99', '204,153,153'],\n  ['枯緑', '#d3d37c', '211,124,124'],\n  ['铜緑色', '#a6d279', '166,121,121'],\n  ['嫩葱緑', '#93b469', '147,105,105'],\n  ['暗卡其色', '#bdb76b', '189,107,107'],\n  ['青豆色', '#98bc67', '152,103,103'],\n  ['苦瓜緑', '#e1ff86', '225,134,134'],\n  ['卡其緑', '#717234', '113,52,52'],\n  ['暗橄榄绿', '#556b2f', '85,47,47'],\n  ['杨桃色', '#eefd6a', '238,106,106'],\n  ['嫩姜黄', '#fef263', '254,99,99'],\n  ['芥末黄', '#cccc4d', '204,77,77'],\n  ['黄緑', '#8cb33e', '140,62,62'],\n  ['草绿', '#99e64d', '153,77,77'],\n  ['月黄', '#ffff4d', '255,77,77'],\n  ['槐黄', '#ffff4d', '255,77,77'],\n  ['藤黄', '#faed4b', '250,75,75'],\n  ['苔藓绿', '#697723', '105,35,35'],\n  ['橄榄军服绿', '#6b8e23', '107,35,35'],\n  ['黄绿', '#9acd32', '154,50,50'],\n  ['含羞草黄', '#e6d933', '230,51,51'],\n  ['绿黄', '#adff2f', '173,47,47'],\n  ['嫩草緑', '#a3b61f', '163,31,31'],\n  ['橄榄緑', '#49460c', '73,12,12'],\n  ['柠檬黄', '#ffff24', '255,36,36'],\n  ['草黄', '#f4ea20', '244,32,32'],\n  ['深芽緑', '#739b06', '115,6,6'],\n  ['军緑', '#3d6402', '61,2,2'],\n  ['鲜黄', '#ffff00', '255,0,0'],\n  ['黄色', '#ffff00', '255,0,0'],\n  ['亮柠檬绿', '#ccff00', '204,0,0'],\n  ['查特酒绿', '#7fff00', '127,0,0'],\n  ['草坪绿', '#7cfc00', '124,0,0'],\n  ['苹果绿', '#8ce600', '140,0,0'],\n  ['橄榄色', '#808000', '128,0,0'],\n  ['新芽色', '#7d7d00', '125,0,0'],\n  ['蜜瓜绿', '#f0fff0', '240,240,240'],\n  ['织锦灰', '#7b8c7c', '123,124,124'],\n  ['竜泉青瓷釉色', '#c8e6c6', '200,198,198'],\n  ['暗灰', '#465146', '70,70,70'],\n  ['深灰', '#353e35', '53,53,53'],\n  ['橄揽灰', '#75856d', '117,109,109'],\n  ['冬灰色', '#63715b', '99,91,91'],\n  ['苔灰', '#425140', '66,64,64'],\n  ['暗海绿', '#8fbc8f', '143,143,143'],\n  ['暗苔緑', '#405742', '64,66,66'],\n  ['灰绿', '#98fb98', '152,152,152'],\n  ['亮绿', '#90ee90', '144,144,144'],\n  ['荷茎緑', '#a0d878', '160,120,120'],\n  ['果緑', '#8ace57', '138,87,87'],\n  ['葱緑', '#99f158', '153,88,88'],\n  ['钴绿', '#66ff59', '102,89,89'],\n  ['叶绿', '#73b839', '115,57,57'],\n  ['嫩绿', '#99ff4d', '153,77,77'],\n  ['常春藤绿', '#36bf36', '54,54,54'],\n  ['柠檬绿', '#32cd32', '50,50,50'],\n  ['森林绿', '#228b22', '34,34,34'],\n  ['鹦鹉緑', '#156a1e', '21,30,30'],\n  ['孔雀石绿', '#22c32e', '34,46,46'],\n  ['双緑', '#043403', '4,3,3'],\n  ['明绿', '#66ff00', '102,0,0'],\n  ['鲜绿色', '#00ff00', '0,0,0'],\n  ['绿色', '#008000', '0,0,0'],\n  ['暗绿', '#006400', '0,0,0'],\n  ['鉄灰', '#3e403f', '62,63,63'],\n  ['薄荷奶油色', '#f5fffa', '245,250,250'],\n  ['深瓦灰', '#717774', '113,116,116'],\n  ['大理石色', '#c9d8cd', '201,205,205'],\n  ['淡灰緑', '#c0d6cb', '192,203,203'],\n  ['粗晶梍', '#323836', '50,54,54'],\n  ['枝緑', '#dffff5', '223,245,245'],\n  ['淡緑', '#dbfbe3', '219,227,227'],\n  ['青虾色', '#6f847d', '111,125,125'],\n  ['浅水緑', '#ccfbea', '204,234,234'],\n  ['蟹青', '#6f9388', '111,136,136'],\n  ['油緑', '#2b3c2f', '43,47,47'],\n  ['三緑', '#b3ffbc', '179,188,188'],\n  ['苍色', '#a6ffcc', '166,204,204'],\n  ['墨緑', '#1e3124', '30,36,36'],\n  ['老緑', '#2d5131', '45,49,49'],\n  ['碧蓝色', '#7fffd4', '127,212,212'],\n  ['青瓷绿', '#73e68c', '115,140,140'],\n  ['中碧蓝色', '#66cdaa', '102,170,170'],\n  ['玉緑', '#6ff9c1', '111,193,193'],\n  ['夜緑色', '#285b41', '40,65,65'],\n  ['冬緑', '#337d56', '51,86,86'],\n  ['碧绿', '#50c878', '80,120,120'],\n  ['緑琉璃色', '#296939', '41,57,57'],\n  ['中海绿', '#3cb371', '60,113,113'],\n  ['绿松石绿', '#4de680', '77,128,128'],\n  ['冬瓜緑', '#349e69', '52,105,105'],\n  ['海绿', '#2e8b57', '46,87,87'],\n  ['铬绿', '#127436', '18,54,54'],\n  ['薄荷绿', '#16982b', '22,43,43'],\n  ['粗孔雀緑', '#028255', '2,85,85'],\n  ['深緑宝石', '#02774f', '2,79,79'],\n  ['巴黎緑', '#02b340', '2,64,64'],\n  ['翡翠', '#015437', '1,55,55'],\n  ['春绿', '#00ff80', '0,128,128'],\n  ['中春绿色', '#00fa9a', '0,154,154'],\n  ['孔雀绿', '#00a15c', '0,92,92'],\n  ['正灰', '#bcc7c7', '188,199,199'],\n  ['鸭蛋青', '#e6fffd', '230,253,253'],\n  ['浅青色', '#e0f3f8', '224,248,248'],\n  ['月白色', '#daf3ef', '218,239,239'],\n  ['云灰', '#cbe2e4', '203,228,228'],\n  ['亮青', '#e0ffff', '224,255,255'],\n  ['蓝灰色', '#8ba39e', '139,158,158'],\n  ['春蓝', '#98b4b3', '152,179,179'],\n  ['石板色', '#4a595b', '74,91,91'],\n  ['婴儿粉蓝', '#b0e0e6', '176,230,230'],\n  ['水色', '#afdfe4', '175,228,228'],\n  ['亮蓝', '#add8e6', '173,230,230'],\n  ['灰绿松石色', '#afeeee', '175,238,238'],\n  ['浅鲜緑', '#b6fee9', '182,233,233'],\n  ['鸠灰', '#9ec8da', '158,218,218'],\n  ['湖蓝灰', '#425c60', '66,96,96'],\n  ['蟹蓝', '#597e82', '89,130,130'],\n  ['灰蓝', '#477066', '71,102,102'],\n  ['黛蓝', '#131e1e', '19,30,30'],\n  ['果灰', '#7ecdb6', '126,182,182'],\n  ['碧玉石', '#66a8a8', '102,168,168'],\n  ['灰緑', '#5c968a', '92,138,138'],\n  ['军服蓝', '#5f9ea0', '95,160,160'],\n  ['暗岩灰', '#2f4f4f', '47,79,79'],\n  ['浅松緑', '#79d2d2', '121,210,210'],\n  ['天蓝', '#87ceeb', '135,235,235'],\n  ['玉石蓝', '#427371', '66,113,113'],\n  ['毛緑', '#28414a', '40,74,74'],\n  ['水蓝', '#66ffe6', '102,230,230'],\n  ['萨克斯蓝', '#4798b3', '71,179,179'],\n  ['深松緑', '#42bfac', '66,172,172'],\n  ['中绿松石色', '#48d1cc', '72,204,204'],\n  ['绿松石色', '#30d5c8', '48,200,200'],\n  ['土耳其蓝', '#33e6cc', '51,204,204'],\n  ['亮海绿', '#20b2aa', '32,170,170'],\n  ['青蓝', '#0dbf8c', '13,140,140'],\n  ['品緑', '#07817a', '7,122,122'],\n  ['湖緑', '#015f5a', '1,90,90'],\n  ['青色', '#00ffff', '0,255,255'],\n  ['深天蓝', '#00bfff', '0,255,255'],\n  ['暗绿松石色', '#00ced1', '0,209,209'],\n  ['暗青', '#008b8b', '0,139,139'],\n  ['孔雀蓝', '#00808c', '0,140,140'],\n  ['凫绿', '#008080', '0,128,128'],\n  ['浓蓝', '#006374', '0,116,116'],\n  ['沙緑', '#003129', '0,41,41'],\n  ['银蓝', '#e1e3e6', '225,230,230'],\n  ['爱丽丝蓝', '#f0f8ff', '240,255,255'],\n  ['鉄青', '#292c2e', '41,46,46'],\n  ['亮钢蓝', '#b0c4de', '176,222,222'],\n  ['亮岩灰', '#778899', '119,153,153'],\n  ['岩灰', '#708090', '112,144,144'],\n  ['花青', '#576d93', '87,147,147'],\n  ['鹊灰', '#283144', '40,68,68'],\n  ['钴蓝', '#6b9bb8', '107,184,184'],\n  ['浅蓝', '#89cff0', '137,240,240'],\n  ['绀青', '#3f4470', '63,112,112'],\n  ['浅天蓝', '#87cefa', '135,250,250'],\n  ['竹月色', '#6b9ac9', '107,201,201'],\n  ['栢坊灰蓝', '#41627c', '65,124,124'],\n  ['灰丁宁蓝', '#5e86c1', '94,193,193'],\n  ['韦奇伍德瓷蓝', '#5686bf', '86,191,191'],\n  ['矢车菊蓝', '#6495ed', '100,237,237'],\n  ['钢青色', '#4682b4', '70,180,180'],\n  ['浅海昌蓝', '#2f4988', '47,136,136'],\n  ['鼠尾草蓝', '#4d80e6', '77,230,230'],\n  ['労动布色', '#1c2859', '28,89,89'],\n  ['品蓝', '#4169e1', '65,225,225'],\n  ['暗矿蓝', '#24367d', '36,125,125'],\n  ['深毛月色', '#284f95', '40,149,149'],\n  ['沙青', '#205580', '32,128,128'],\n  ['琉璃蓝', '#183a65', '24,101,101'],\n  ['浅土蓝', '#122c4b', '18,75,75'],\n  ['绒蓝', '#1f4f89', '31,137,137'],\n  ['北京毛蓝', '#1f4f89', '31,137,137'],\n  ['深竹月', '#2e62cd', '46,205,205'],\n  ['毛月色', '#2c80c5', '44,197,197'],\n  ['蔚蓝', '#2a52be', '42,190,190'],\n  ['深蓝', '#0e1a49', '14,73,73'],\n  ['蔵蓝', '#111f6a', '17,106,106'],\n  ['道奇蓝', '#1e90ff', '30,255,255'],\n  ['海蓝', '#0b2d64', '11,100,100'],\n  ['蓝宝石色', '#082567', '8,103,103'],\n  ['深毛蓝', '#031025', '3,37,37'],\n  ['天青石蓝', '#0d33ff', '13,255,255'],\n  ['郡青', '#043ab9', '4,185,185'],\n  ['湛蓝', '#007fff', '0,255,255'],\n  ['极浓海蓝', '#0033ff', '0,255,255'],\n  ['国际奇连蓝', '#002fa7', '0,167,167'],\n  ['矿蓝', '#004d99', '0,153,153'],\n  ['暗婴儿粉蓝', '#003399', '0,153,153'],\n  ['水手蓝', '#00477d', '0,125,125'],\n  ['普鲁士蓝', '#003153', '0,83,83'],\n  ['雪灰', '#ededef', '237,239,239'],\n  ['幽灵白', '#f8f8ff', '248,255,255'],\n  ['薰衣草紫', '#e6e6fa', '230,250,250'],\n  ['雪青灰', '#bfbed3', '191,211,211'],\n  ['闪蝶紫', '#b4a4ca', '180,202,202'],\n  ['矿紫', '#b8a1cf', '184,207,207'],\n  ['青金色', '#9fa0d7', '159,215,215'],\n  ['罗蓝灰', '#3c374a', '60,74,74'],\n  ['浅灰紫红', '#8674a1', '134,161,161'],\n  ['紫水晶', '#d9b3ff', '217,255,255'],\n  ['紫丁香色', '#b399ff', '179,255,255'],\n  ['浅石英紫', '#cc99ff', '204,255,255'],\n  ['蔵墨蓝', '#27243c', '39,60,60'],\n  ['雪蓝', '#777bce', '119,206,206'],\n  ['中紫红', '#9370db', '147,219,219'],\n  ['靛蓝', '#201f3d', '32,61,61'],\n  ['木槿紫', '#bf80ff', '191,255,255'],\n  ['钛鉄', '#1a152b', '26,43,43'],\n  ['中岩蓝', '#7b68ee', '123,238,238'],\n  ['岩蓝', '#6a5acd', '106,205,205'],\n  ['暗岩蓝', '#483d8b', '72,139,139'],\n  ['紫藤色', '#5c50e6', '92,230,230'],\n  ['紫水晶色', '#6633cc', '102,204,204'],\n  ['午夜蓝', '#191970', '25,112,112'],\n  ['宝石蓝', '#1e25bb', '30,187,187'],\n  ['鲜蓝', '#1515f9', '21,249,249'],\n  ['蓝色', '#0000ff', '0,255,255'],\n  ['中蓝', '#0000cd', '0,205,205'],\n  ['缬草紫', '#5000b8', '80,184,184'],\n  ['暗蓝', '#00008b', '0,139,139'],\n  ['藏青', '#000080', '0,128,128'],\n  ['浅藤紫', '#ece4f3', '236,243,243'],\n  ['淡紫丁香色', '#e6cfe6', '230,230,230'],\n  ['蓟紫', '#d8bfd8', '216,216,216'],\n  ['莲灰', '#d1bfdd', '209,221,221'],\n  ['紫藤灰', '#b5a4c4', '181,196,196'],\n  ['铁线莲紫', '#cca3cc', '204,204,204'],\n  ['梅红色', '#dda0dd', '221,221,221'],\n  ['紫薇花', '#ff99ff', '255,255,255'],\n  ['凤仙紫', '#9565b1', '149,177,177'],\n  ['丁香紫', '#503a65', '80,101,101'],\n  ['亮紫', '#ee82ee', '238,238,238'],\n  ['兰紫', '#da70d6', '218,214,214'],\n  ['洋葱紫', '#8e488e', '142,142,142'],\n  ['优品紫红', '#e680ff', '230,255,255'],\n  ['浅凤仙紫', '#e666ff', '230,255,255'],\n  ['中兰紫', '#ba55d3', '186,211,211'],\n  ['紫罗蓝', '#732e7e', '115,126,126'],\n  ['锦葵紫', '#d94dff', '217,255,255'],\n  ['暗兰紫', '#9932cc', '153,204,204'],\n  ['蓝紫', '#8a2be2', '138,226,226'],\n  ['紫罗兰色', '#8b00ff', '139,255,255'],\n  ['洋红', '#ff00ff', '255,255,255'],\n  ['暗紫', '#9400d3', '148,211,211'],\n  ['三色堇紫', '#7400a1', '116,161,161'],\n  ['青莲紫', '#730099', '115,153,153'],\n  ['暗洋红', '#8b008b', '139,139,139'],\n  ['靛色', '#4b0080', '75,128,128'],\n  ['紫色', '#800080', '128,128,128'],\n  ['竜胆紫', '#1a0033', '26,51,51'],\n  ['白色', '#ffffff', '255,255,255'],\n  ['立徳粉', '#ffffff', '255,255,255'],\n  ['白烟色', '#f5f5f5', '245,245,245'],\n  ['庚斯博罗灰', '#dcdcdc', '220,220,220'],\n  ['亮灰色', '#d3d3d3', '211,211,211'],\n  ['银箔', '#d4d4d4', '212,212,212'],\n  ['银色', '#c0c0c0', '192,192,192'],\n  ['灰色', '#808080', '128,128,128'],\n  ['银鼠灰', '#797979', '121,121,121'],\n  ['昏灰', '#696969', '105,105,105'],\n  ['青灰色', '#484848', '72,72,72'],\n  ['百草霜', '#1c1c1c', '28,28,28'],\n  ['黑色', '#000000', '0,0,0'],\n  ['妃色', '#ED5736', '237,87,54'],\n  ['桃红', '#F47983', '244,121,131'],\n  ['海棠红', '#DB5A6B', '219,90,107'],\n  ['石榴红', '#F20C00', '242,12,0'],\n  ['樱桃色', '#C93756', '201,55,86'],\n  ['大红', '#FF2121', '255,33,33'],\n  ['胭脂', '#9D2933', '157,41,51'],\n  ['丹', '#FF4E20', '255,78,32'],\n  ['彤', '#F35336', '243,83,54'],\n  ['茜色', '#CB3A56', '203,58,86'],\n  ['火红', '#FF2D51', '255,45,81'],\n  ['赫赤', '#C91F37', '201,31,55'],\n  ['嫣红', '#EF7A82', '239,122,130'],\n  ['炎', '#FF3300', '255,51,0'],\n  ['赤', '#C3272B', '195,39,43'],\n  ['绾', '#A98175', '169,129,117'],\n  ['檀', '#B36D61', '179,109,97'],\n  ['殷红', '#BE002F', '190,0,47'],\n  ['酡红', '#DC3023', '220,48,35'],\n  ['酡颜', '#F9906F', '249,144,111'],\n  ['鸭黄', '#FAFF72', '250,255,114'],\n  ['樱草色', '#EAFF56', '234,255,86'],\n  ['杏红', '#FF8C31', '255,140,​​49'],\n  ['橘黄', '#FF8936', '255,137,54'],\n  ['橙黄', '#FFA400', '255,164,0'],\n  ['橘红', '#FF7500', '255,117,0'],\n  ['缃色', '#F0C239', '240,194,57'],\n  ['茶色', '#B35C44', '179,92,68'],\n  ['昏黄', '#C89B40', '200,155,64'],\n  ['棕色', '#B25D25', '178,93,37'],\n  ['棕绿', '#827100', '130,113,0'],\n  ['棕黑', '#7C4B00', '124,75,0'],\n  ['棕黄', '#AE7000', '174,112,0'],\n  ['琥珀', '#CA6924', '202,105,36'],\n  ['枯黄', '#D3B17D', '211,177,125'],\n  ['黄栌', '#E29C45', '226,156,69'],\n  ['秋色', '#896C39', '137,108,57'],\n  ['秋香色', '#D9B611', '217,182,17'],\n  ['柳黄', '#C9DD22', '201,221,34'],\n  ['柳绿', '#AFDD22', '175,221,34'],\n  ['竹青', '#789262', '120,146,98'],\n  ['葱黄', '#A3D900', '163,217,0'],\n  ['葱绿', '#9ED900', '158,217,0'],\n  ['葱青', '#0EB83A', '14,184,58'],\n  ['青葱', '#0AA344', '10,163,68'],\n  ['油绿', '#00BC12', '0,188,18'],\n  ['绿沉', '#0C8918', '12,137,24'],\n  ['碧色', '#1BD1A5', '27,209,165'],\n  ['青碧', '#48C0A3', '72,192,163'],\n  ['翡翠色', '#3DE1AD', '61,225,173'],\n  ['青翠', '#00E079', '0,224,121'],\n  ['青白', '#C0EBD7', '192,235,215'],\n  ['鸭卵青', '#E0EEE8', '224,238,232'],\n  ['蟹壳青', '#BBCDC5', '187,205,197'],\n  ['鸦青', '#424C50', '66,76,80'],\n  ['豆绿', '#9ED048', '158,208,72'],\n  ['豆青', '#96CE54', '150,206,84'],\n  ['石青', '#7BCFA6', '123,207,166'],\n  ['玉色', '#7BCFA6', '46,223,163'],\n  ['缥', '#7FECAD', '127,236,173'],\n  ['艾绿', '#A4E2C6', '164,226,198'],\n  ['松柏绿', '#21A675', '33,166,117'],\n  ['松花绿', '#057748', '5,119,72'],\n  ['松花色', '#BCE672', '5,119,72'],\n  ['蓝', '#44CEF6', '68,206,246'],\n  ['靛青', '#177CB0', '23,124,176'],\n  ['碧蓝', '#3EEDE7', '62,237,231'],\n  ['宝蓝', '#4B5CC4', '75,92,196'],\n  ['藏蓝', '#3B2E7E', '59,46,126'],\n  ['黛', '#4A4266', '74,66,102'],\n  ['黛绿', '#426666', '66,102,102'],\n  ['黛紫', '#574266', '87,66,102'],\n  ['紫酱', '#815463', '129,84,99'],\n  ['酱紫', '#815476', '129,84,118'],\n  ['紫檀', '#4C221B', '76,34,27'],\n  ['紫棠', '#56004F', '86,0,79'],\n  ['青莲', '#801DAE', '128,29,174'],\n  ['群青', '#4C8DAE', '76,141,174'],\n  ['雪青', '#B0A4E3', '176,164,227'],\n  ['丁香色', '#CCA4E3', '204,164,227'],\n  ['藕色', '#EDD1D8', '237,209,216'],\n  ['藕荷色', '#E4C6D0', '228,198,208'],\n  ['苍青', '#A29B7C', '162,155,124'],\n  ['苍黑', '#7397AB', '115,151,171'],\n  ['苍白', '#D1D9E0', '209,217,224'],\n  ['水红', '#F3D3E7', '243,211,231'],\n  ['水绿', '#D4F2E7', '212,242,231'],\n  ['淡青', '#D3E0F3', '211,224,243'],\n  ['湖蓝', '#30DFF3', '48,2​​23,243'],\n  ['湖绿', '#25F8CB', '37,248,203'],\n  ['精白', '#FFFFFF', '255,255,255'],\n  ['像牙白', '#FFFBF0', '255,251,240'],\n  ['雪白', '#F0FCFF', '240,252,255'],\n  ['月白', '#D6ECF0', '214,236,240'],\n  ['缟', '#F2ECDE', '242,236,222'],\n  ['素', '#E0F0E9', '224,240,233'],\n  ['荼白', '#F3F9F1', '243,249,241'],\n  ['霜色', '#E9F1F6', '233,241,246'],\n  ['花白', '#C2CCD0', '194,204,208'],\n  ['鱼肚白', '#FCEFE8', '252,239,232'],\n  ['莹白', '#E3F9FD', '227,239,253'],\n  ['牙色', '#EEDEB0', '238,222,176'],\n  ['铅白', '#F0F0F4', '240,240,244'],\n  ['玄色', '#622A1D', '98,42,29'],\n  ['玄青', '#3D3B4F', '61,59,79'],\n  ['乌色', '#725E82', '114,94,130'],\n  ['乌黑', '#392F41', '57,47,65'],\n  ['漆黑', '#161823', '22,24,35'],\n  ['墨色', '#50616D', '80,97,109'],\n  ['墨灰', '#758A99', '117,138,153'],\n  ['缁色', '#493131', '73,49,49'],\n  ['煤黑', '#312520', '49,37,32'],\n  ['黧', '#5D513C', '93,81,60'],\n  ['黎', '#75664D', '117,102,77'],\n  ['黝', '#6B6882', '107,104,130'],\n  ['黝黑', '#665757', '102,87,87'],\n  ['黯', '#41555D', '65,85,93'],\n  ['赤金', '#F2BE45', '242,190,69'],\n  ['银白', '#E9E7EF', '233,231,239'],\n  ['铜绿', '#549688', '84,150,136'],\n  ['乌金', '#A78E44', '167,142,68'],\n  ['老银', '#BACAC6', '186,202,198'],\n  ['猩红色', '#FF2400', '255,36,0'],\n  ['朱红色', '#FF4D00', '255,77,0'],\n  ['柠檬绿色', '#CCFF00', '204,255,0'],\n  ['黄绿色', '#66FF00', '102,255,0'],\n  ['蔚蓝色', '#007FFF', '0,127,255'],\n  ['蓝绿色', '#7FFFD4', '127,255,212'],\n  ['浅蓝色', '#E0FFFF', '137,207,240'],\n  ['绿松色', '#30D5C8', '48,213,200'],\n  ['粉末蓝', '#003399', '0,51,153'],\n  ['皇室蓝', '#4169E1', '65,105,225'],\n  ['天青蓝', '#2A52BE', '42,82,190'],\n  ['钴蓝色', '#0047AB', '0,71,171'],\n  ['海军蓝', '#000080', '0,0,128'],\n  ['白牛仔布色', '#5E86C1', '94,134,193'],\n  ['青玉色', '#082567', '8,37,103'],\n  ['深茜红', '#E32636', '227,38,54'],\n  ['洋红色', '#FF00FF', '255,0,255'],\n  ['橙黄色', '#FFCC00', '255,204,0'],\n  ['春绿色', '#00FF80', '0,255,128'],\n  ['鸭绿色', '#008080', '0,128,128'],\n  ['鲑肉色', '#FF8C69', '255,140,105'],\n  ['米色', '#F5F5DC', '245,245,210'],\n  ['薰衣草色', '#E6E6FA', '230,230,250'],\n  ['兰花色', '#DA70D6', '218,112,214'],\n  ['粉红色', '#FFC0CB', '255,192,203'],\n  ['燃橙色', '#CC5500', '204,85,0'],\n  ['暗灰色', '#404040', '64,64,64'],\n];\n\nexport const colors: string[] = [\n  '赤',\n  '朱',\n  '丹',\n  '绯',\n  '彤',\n  '绛',\n  '茜',\n  '纁',\n  '赭',\n  '栗',\n  '褐',\n  '驼',\n  '赭',\n  '橘',\n  '曙',\n  '翠',\n  '碧',\n  '金',\n  '米',\n  '缃',\n  '靛',\n  '紫',\n  '藕',\n  '桃',\n  '青',\n  '玄',\n  '皂',\n  '乌',\n  '墨',\n  '黛',\n  '黝',\n  '素',\n  '杏',\n  '缟',\n  '鹤',\n  '皓',\n  '苍',\n  '华',\n  '银',\n  ...COLOR_WITH_RGB.map(item => item[0]),\n];\n", "// @flow\nimport { Optimizer } from './BaseModule';\nimport type { SegmentToken } from './type';\n\nimport { colors } from './COLORS';\n\n// 把一些错认为名词的词标注为形容词，或者对名词作定语的情况\nexport default class AdjectiveOptimizer extends Optimizer {\n  doOptimize(words: Array<SegmentToken>): Array<SegmentToken> {\n    const { POSTAG } = this.segment;\n    let index = 0;\n    while (index < words.length) {\n      const word = words[index];\n      const nextword = words[index + 1];\n      if (nextword) {\n        // 对于<颜色>+<的>，直接判断颜色是形容词（字典里颜色都是名词）\n        if (nextword.p === POSTAG.D_U && colors.includes(word.w)) {\n          word.p = POSTAG.D_A;\n        }\n        // 如果是连续的两个名词，前一个是颜色，那这个颜色也是形容词\n        if (word.p === POSTAG.D_N && this.isNominal(nextword.p) && colors.includes(word.w)) {\n          word.p = POSTAG.D_A;\n        }\n      }\n      // 移到下一个单词\n      index += 1;\n    }\n    return words;\n  }\n\n  isNominal(pos: number | number[]): boolean {\n    if (Array.isArray(pos)) {\n      return this.isNominal(pos[0]);\n    }\n    const { POSTAG } = this.segment;\n    return (\n      pos === POSTAG.D_N\n      || pos === POSTAG.A_NT\n      || pos === POSTAG.A_NX\n      || pos === POSTAG.A_NZ\n      || pos === POSTAG.A_NR\n      || pos === POSTAG.A_NS\n      || pos === POSTAG.URL\n    );\n  }\n}\n", "// @flow\n\n/**\n * 中文姓\n */\n\nfunction addOrderInfo(chars: string[], order: number): { [char: string]: number } {\n  const result = {};\n  chars.forEach(char => {\n    result[char] = order;\n  });\n  return result;\n}\n\n// 单姓\nexport const FAMILY_NAME_1 = addOrderInfo(\n  [\n    // 有明显歧义的姓氏\n    '王',\n    '张',\n    '黄',\n    '周',\n    '徐',\n    '胡',\n    '高',\n    '林',\n    '马',\n    '于',\n    '程',\n    '傅',\n    '曾',\n    '叶',\n    '余',\n    '夏',\n    '钟',\n    '田',\n    '任',\n    '方',\n    '石',\n    '熊',\n    '白',\n    '毛',\n    '江',\n    '史',\n    '候',\n    '龙',\n    '万',\n    '段',\n    '雷',\n    '钱',\n    '汤',\n    '易',\n    '常',\n    '武',\n    '赖',\n    '文',\n    '查',\n    // 没有明显歧义的姓氏\n    '赵',\n    '肖',\n    '孙',\n    '李',\n    '吴',\n    '郑',\n    '冯',\n    '陈',\n    '褚',\n    '卫',\n    '蒋',\n    '沈',\n    '韩',\n    '杨',\n    '朱',\n    '秦',\n    '尤',\n    '许',\n    '何',\n    '吕',\n    '施',\n    '桓',\n    '孔',\n    '曹',\n    '严',\n    '华',\n    '金',\n    '魏',\n    '陶',\n    '姜',\n    '戚',\n    '谢',\n    '邹',\n    '喻',\n    '柏',\n    '窦',\n    '苏',\n    '潘',\n    '葛',\n    '奚',\n    '范',\n    '彭',\n    '鲁',\n    '韦',\n    '昌',\n    '俞',\n    '袁',\n    '酆',\n    '鲍',\n    '唐',\n    '费',\n    '廉',\n    '岑',\n    '薛',\n    '贺',\n    '倪',\n    '滕',\n    '殷',\n    '罗',\n    '毕',\n    '郝',\n    '邬',\n    '卞',\n    '康',\n    '卜',\n    '顾',\n    '孟',\n    '穆',\n    '萧',\n    '尹',\n    '姚',\n    '邵',\n    '湛',\n    '汪',\n    '祁',\n    '禹',\n    '狄',\n    '贝',\n    '臧',\n    '伏',\n    '戴',\n    '宋',\n    '茅',\n    '庞',\n    '纪',\n    '舒',\n    '屈',\n    '祝',\n    '董',\n    '梁',\n    '杜',\n    '阮',\n    '闵',\n    '贾',\n    '娄',\n    '颜',\n    '郭',\n    '邱',\n    '骆',\n    '蔡',\n    '樊',\n    '凌',\n    '霍',\n    '虞',\n    '柯',\n    '昝',\n    '卢',\n    '柯',\n    '缪',\n    '宗',\n    '丁',\n    '贲',\n    '邓',\n    '郁',\n    '杭',\n    '洪',\n    '崔',\n    '龚',\n    '嵇',\n    '邢',\n    '滑',\n    '裴',\n    '陆',\n    '荣',\n    '荀',\n    '惠',\n    '甄',\n    '芮',\n    '羿',\n    '储',\n    '靳',\n    '汲',\n    '邴',\n    '糜',\n    '隗',\n    '侯',\n    '宓',\n    '蓬',\n    '郗',\n    '仲',\n    '栾',\n    '钭',\n    '历',\n    '戎',\n    '刘',\n    '詹',\n    '幸',\n    '韶',\n    '郜',\n    '黎',\n    '蓟',\n    '溥',\n    '蒲',\n    '邰',\n    '鄂',\n    '咸',\n    '卓',\n    '蔺',\n    '屠',\n    '乔',\n    '郁',\n    '胥',\n    '苍',\n    '莘',\n    '翟',\n    '谭',\n    '贡',\n    '劳',\n    '冉',\n    '郦',\n    '雍',\n    '璩',\n    '桑',\n    '桂',\n    '濮',\n    '扈',\n    '冀',\n    '浦',\n    '庄',\n    '晏',\n    '瞿',\n    '阎',\n    '慕',\n    '茹',\n    '习',\n    '宦',\n    '艾',\n    '容',\n    '慎',\n    '戈',\n    '廖',\n    '庾',\n    '衡',\n    '耿',\n    '弘',\n    '匡',\n    '阙',\n    '殳',\n    '沃',\n    '蔚',\n    '夔',\n    '隆',\n    '巩',\n    '聂',\n    '晁',\n    '敖',\n    '融',\n    '訾',\n    '辛',\n    '阚',\n    '毋',\n    '乜',\n    '鞠',\n    '丰',\n    '蒯',\n    '荆',\n    '竺',\n    '盍',\n    '单',\n    '欧',\n  ],\n  1,\n);\n// 复姓\nexport const FAMILY_NAME_2 = addOrderInfo(\n  [\n    '司马',\n    '上官',\n    '欧阳',\n    '夏侯',\n    '诸葛',\n    '闻人',\n    '东方',\n    '赫连',\n    '皇甫',\n    '尉迟',\n    '公羊',\n    '澹台',\n    '公冶',\n    '宗政',\n    '濮阳',\n    '淳于',\n    '单于',\n    '太叔',\n    '申屠',\n    '公孙',\n    '仲孙',\n    '轩辕',\n    '令狐',\n    '徐离',\n    '宇文',\n    '长孙',\n    '慕容',\n    '司徒',\n    '司空',\n    '万俟',\n  ],\n  2,\n);\n\n// 双字姓名第一个字\nexport const DOUBLE_NAME_1 = addOrderInfo(\n  [\n    '阿',\n    '建',\n    '小',\n    '晓',\n    '文',\n    '志',\n    '国',\n    '玉',\n    '丽',\n    '永',\n    '海',\n    '春',\n    '金',\n    '明',\n    '新',\n    '德',\n    '秀',\n    '红',\n    '亚',\n    '伟',\n    '雪',\n    '俊',\n    '桂',\n    '爱',\n    '美',\n    '世',\n    '正',\n    '庆',\n    '学',\n    '家',\n    '立',\n    '淑',\n    '振',\n    '云',\n    '华',\n    '光',\n    '惠',\n    '兴',\n    '天',\n    '长',\n    '艳',\n    '慧',\n    '利',\n    '宏',\n    '佳',\n    '瑞',\n    '凤',\n    '荣',\n    '秋',\n    '继',\n    '嘉',\n    '卫',\n    '燕',\n    '思',\n    '维',\n    '少',\n    '福',\n    '忠',\n    '宝',\n    '子',\n    '成',\n    '月',\n    '洪',\n    '东',\n    '一',\n    '泽',\n    '林',\n    '大',\n    '素',\n    '旭',\n    '宇',\n    '智',\n    '锦',\n    '冬',\n    '玲',\n    '雅',\n    '伯',\n    '翠',\n    '传',\n    '启',\n    '剑',\n    '安',\n    '树',\n    '良',\n    '中',\n    '梦',\n    '广',\n    '昌',\n    '元',\n    '万',\n    '清',\n    '静',\n    '友',\n    '宗',\n    '兆',\n    '丹',\n    '克',\n    '彩',\n    '绍',\n    '喜',\n    '远',\n    '朝',\n    '敏',\n    '培',\n    '胜',\n    '祖',\n    '先',\n    '菊',\n    '士',\n    '向',\n    '有',\n    '连',\n    '军',\n    '健',\n    '巧',\n    '耀',\n    '莉',\n    '英',\n    '方',\n    '和',\n    '仁',\n    '孝',\n    '梅',\n    '汉',\n    '兰',\n    '松',\n    '水',\n    '江',\n    '益',\n    '开',\n    '景',\n    '运',\n    '贵',\n    '祥',\n    '青',\n    '芳',\n    '碧',\n    '婷',\n    '龙',\n    '鹏',\n    '自',\n    '顺',\n    '双',\n    '书',\n    '生',\n    '义',\n    '跃',\n    '银',\n    '佩',\n    '雨',\n    '保',\n    '贤',\n    '仲',\n    '鸿',\n    '浩',\n    '加',\n    '定',\n    '炳',\n    '飞',\n    '锡',\n    '柏',\n    '发',\n    '超',\n    '道',\n    '怀',\n    '进',\n    '其',\n    '富',\n    '平',\n    '全',\n    '阳',\n    '吉',\n    '茂',\n    '彦',\n    '诗',\n    '洁',\n    '润',\n    '承',\n    '治',\n    '焕',\n    '如',\n    '君',\n    '增',\n    '善',\n    '希',\n    '根',\n    '应',\n    '勇',\n    '宜',\n    '守',\n    '会',\n    '凯',\n    '育',\n    '湘',\n    '凌',\n    '本',\n    '敬',\n    '博',\n    '延',\n    '乐',\n    '三',\n    '二',\n    '四',\n    '五',\n    '六',\n    '七',\n    '八',\n    '九',\n    '十',\n  ],\n  1,\n);\n\n// 双字姓名第二个字\nexport const DOUBLE_NAME_2 = addOrderInfo(\n  [\n    '华',\n    '平',\n    '明',\n    '英',\n    '军',\n    '林',\n    '萍',\n    '芳',\n    '玲',\n    '红',\n    '生',\n    '霞',\n    '梅',\n    '文',\n    '荣',\n    '珍',\n    '兰',\n    '娟',\n    '峰',\n    '琴',\n    '云',\n    '辉',\n    '东',\n    '龙',\n    '敏',\n    '伟',\n    '强',\n    '丽',\n    '春',\n    '杰',\n    '燕',\n    '民',\n    '君',\n    '波',\n    '国',\n    '芬',\n    '清',\n    '祥',\n    '斌',\n    '婷',\n    '飞',\n    '良',\n    '忠',\n    '新',\n    '凤',\n    '锋',\n    '成',\n    '勇',\n    '刚',\n    '玉',\n    '元',\n    '宇',\n    '海',\n    '兵',\n    '安',\n    '庆',\n    '涛',\n    '鹏',\n    '亮',\n    '青',\n    '阳',\n    '艳',\n    '松',\n    '江',\n    '莲',\n    '娜',\n    '兴',\n    '光',\n    '德',\n    '武',\n    '香',\n    '俊',\n    '秀',\n    '慧',\n    '雄',\n    '才',\n    '宏',\n    '群',\n    '琼',\n    '胜',\n    '超',\n    '彬',\n    '莉',\n    '中',\n    '山',\n    '富',\n    '花',\n    '宁',\n    '利',\n    '贵',\n    '福',\n    '发',\n    '义',\n    '蓉',\n    '喜',\n    '娥',\n    '昌',\n    '仁',\n    '志',\n    '全',\n    '宝',\n    '权',\n    '美',\n    '琳',\n    '建',\n    '金',\n    '贤',\n    '星',\n    '丹',\n    '根',\n    '和',\n    '珠',\n    '康',\n    '菊',\n    '琪',\n    '坤',\n    '泉',\n    '秋',\n    '静',\n    '佳',\n    '顺',\n    '源',\n    '珊',\n    '达',\n    '欣',\n    '如',\n    '莹',\n    '章',\n    '浩',\n    '勤',\n    '芹',\n    '容',\n    '友',\n    '芝',\n    '豪',\n    '洁',\n    '鑫',\n    '惠',\n    '洪',\n    '旺',\n    '虎',\n    '远',\n    '妮',\n    '森',\n    '妹',\n    '南',\n    '雯',\n    '奇',\n    '健',\n    '卿',\n    '虹',\n    '娇',\n    '媛',\n    '怡',\n    '铭',\n    '川',\n    '进',\n    '博',\n    '智',\n    '来',\n    '琦',\n    '学',\n    '聪',\n    '洋',\n    '乐',\n    '年',\n    '翔',\n    '然',\n    '栋',\n    '凯',\n    '颖',\n    '鸣',\n    '丰',\n    '瑞',\n    '奎',\n    '立',\n    '堂',\n    '威',\n    '雪',\n    '鸿',\n    '晶',\n    '桂',\n    '凡',\n    '娣',\n    '先',\n    '洲',\n    '毅',\n    '雅',\n    '月',\n    '旭',\n    '田',\n    '晖',\n    '方',\n    '恒',\n    '亚',\n    '泽',\n    '风',\n    '银',\n    '高',\n    '贞',\n    '九',\n    '薇',\n  ],\n  2,\n);\n\n// 单字姓名\nexport const SINGLE_NAME = addOrderInfo(\n  [\n    '家',\n    '民',\n    '敏',\n    '伟',\n    '勇',\n    '军',\n    '斌',\n    '静',\n    '丽',\n    '涛',\n    '芳',\n    '杰',\n    '萍',\n    '强',\n    '俊',\n    '明',\n    '燕',\n    '磊',\n    '玲',\n    '华',\n    '平',\n    '鹏',\n    '健',\n    '波',\n    '红',\n    '丹',\n    '辉',\n    '超',\n    '艳',\n    '莉',\n    '刚',\n    '娟',\n    '峰',\n    '婷',\n    '亮',\n    '洁',\n    '颖',\n    '琳',\n    '英',\n    '慧',\n    '飞',\n    '霞',\n    '浩',\n    '凯',\n    '宇',\n    '毅',\n    '林',\n    '佳',\n    '云',\n    '莹',\n    '娜',\n    '晶',\n    '洋',\n    '文',\n    '鑫',\n    '欣',\n    '琴',\n    '宁',\n    '琼',\n    '兵',\n    '青',\n    '琦',\n    '翔',\n    '彬',\n    '锋',\n    '阳',\n    '璐',\n    '旭',\n    '蕾',\n    '剑',\n    '虹',\n    '蓉',\n    '建',\n    '倩',\n    '梅',\n    '宏',\n    '威',\n    '博',\n    '君',\n    '力',\n    '龙',\n    '晨',\n    '薇',\n    '雪',\n    '琪',\n    '欢',\n    '荣',\n    '江',\n    '炜',\n    '成',\n    '庆',\n    '冰',\n    '东',\n    '帆',\n    '雷',\n    '楠',\n    '锐',\n    '进',\n    '海',\n    '凡',\n    '巍',\n    '维',\n    '迪',\n    '媛',\n    '玮',\n    '杨',\n    '群',\n    '瑛',\n    '悦',\n    '春',\n    '瑶',\n    '婧',\n    '兰',\n    '茜',\n    '松',\n    '爽',\n    '立',\n    '瑜',\n    '睿',\n    '晖',\n    '聪',\n    '帅',\n    '瑾',\n    '骏',\n    '雯',\n    '晓',\n    '昊',\n    '勤',\n    '新',\n    '瑞',\n    '岩',\n    '星',\n    '忠',\n    '志',\n    '怡',\n    '坤',\n    '康',\n    '航',\n    '利',\n    '畅',\n    '坚',\n    '雄',\n    '智',\n    '萌',\n    '哲',\n    '岚',\n    '洪',\n    '捷',\n    '珊',\n    '恒',\n    '靖',\n    '清',\n    '扬',\n    '昕',\n    '乐',\n    '武',\n    '玉',\n    '诚',\n    '菲',\n    '锦',\n    '凤',\n    '珍',\n    '晔',\n    '妍',\n    '璇',\n    '胜',\n    '菁',\n    '科',\n    '芬',\n    '露',\n    '越',\n    '彤',\n    '曦',\n    '义',\n    '良',\n    '鸣',\n    '芸',\n    '方',\n    '月',\n    '铭',\n    '光',\n    '震',\n    '冬',\n    '源',\n    '政',\n    '虎',\n    '莎',\n    '彪',\n    '蓓',\n    '钢',\n    '凌',\n    '奇',\n    '卫',\n    '彦',\n    '烨',\n    '可',\n    '黎',\n    '川',\n    '淼',\n    '惠',\n    '祥',\n    '然',\n    '三',\n    '二',\n    '一',\n    '四',\n    '五',\n    '六',\n    '七',\n    '八',\n    '九',\n    '十',\n  ],\n  1,\n);\n\nexport default {\n  FAMILY_NAME_1,\n  FAMILY_NAME_2,\n  DOUBLE_NAME_1,\n  DOUBLE_NAME_2,\n  SINGLE_NAME,\n};\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\nimport {\n  FAMILY_NAME_1, FAMILY_NAME_2, SINGLE_NAME, DOUBLE_NAME_1, DOUBLE_NAME_2,\n} from './CHS_NAMES';\n\nexport default class ChsNameTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    const ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p > 0) {\n        ret.push(word);\n        continue;\n      }\n      // 仅对未识别的词进行匹配\n      const nameinfo: TokenStartPosition = ChsNameTokenizer.matchName(word.w);\n      if (nameinfo.length < 1) {\n        ret.push(word);\n        continue;\n      }\n      // 分离出人名\n      let lastc = 0;\n      for (var ui = 0, url; (url = nameinfo[ui]); ui++) {\n        if (url.c > lastc) {\n          ret.push({ w: word.w.substr(lastc, url.c - lastc) });\n        }\n        ret.push({ w: url.w, p: POSTAG.A_NR });\n        lastc = url.c + url.w.length;\n      }\n      const lastn = nameinfo[nameinfo.length - 1];\n      if (lastn.c + lastn.w.length < word.w.length) {\n        ret.push({ w: word.w.substr(lastn.c + lastn.w.length) });\n      }\n    }\n    return ret;\n  }\n\n  // 匹配包含的人名，并返回相关信息\n  static matchName(text: string, startPos: number): Array<TokenStartPosition> {\n    let startPosition = 0;\n    if (!isNaN(startPos)) startPosition = startPos;\n    const result = [];\n    while (startPosition < text.length) {\n      let name = false;\n      // 取两个字，看看在不在复姓表里\n      const f2 = text.substr(startPosition, 2);\n      if (f2 in FAMILY_NAME_2) {\n        const n1 = text.charAt(startPosition + 2);\n        const n2 = text.charAt(startPosition + 3);\n        // 看看姓后面的字在不在名表里\n        if (n1 in DOUBLE_NAME_1 && n2 in DOUBLE_NAME_2) {\n          name = f2 + n1 + n2;\n        } else if (n1 in SINGLE_NAME) {\n          name = f2 + n1 + (n1 === n2 ? n2 : '');\n        }\n      }\n      // 单姓\n      const f1 = text.charAt(startPosition);\n      if (name === false && f1 in FAMILY_NAME_1) {\n        const n1 = text.charAt(startPosition + 1);\n        const n2 = text.charAt(startPosition + 2);\n        if (n1 in DOUBLE_NAME_1 && n2 in DOUBLE_NAME_2) {\n          name = f1 + n1 + n2;\n        } else if (n1 in SINGLE_NAME) {\n          name = f1 + n1 + (n1 === n2 ? n2 : '');\n        }\n      }\n      // 检查是否匹配成功\n      if (name === false) {\n        startPosition++;\n      } else {\n        result.push({ w: name, c: startPosition });\n        startPosition += name.length;\n      }\n    }\n    return result;\n  }\n}\n", "// @flow\nimport { Optimizer } from './BaseModule';\nimport type { SegmentToken } from './type';\n\nexport default class DictOptimizer extends Optimizer {\n  /**\n   * 词典优化\n   *\n   * @param {array} words 单词数组\n   * @param {bool} isNotFirst 是否为管理器调用的\n   * @return {array}\n   */\n  doOptimize(words: Array<SegmentToken>, isNotFirst: boolean): Array<SegmentToken> {\n    // debug(words);\n    if (typeof isNotFirst === 'undefined') {\n      isNotFirst = false;\n    }\n    // 合并相邻的能组成一个单词的两个词\n    const TABLE = this.segment.getDict('TABLE');\n    const POSTAG = this.segment.POSTAG;\n\n    let i = 0;\n    let ie = words.length - 1;\n    while (i < ie) {\n      const w1 = words[i];\n      const w2 = words[i + 1];\n      // debug(w1.w + ', ' + w2.w);\n\n      // ==========================================\n      // 能组成一个新词的(词性必须相同)\n      const nw = w1.w + w2.w;\n      if (w1.p === w2.p && nw in TABLE) {\n        words.splice(i, 2, {\n          w: nw,\n          p: TABLE[nw].p,\n        });\n        ie--;\n        continue;\n      }\n\n      // 形容词 + 助词 = 形容词，如： 不同 + 的 = 不同的\n      if ((w1.p & POSTAG.D_A) > 0 && w2.p & POSTAG.D_U) {\n        words.splice(i, 2, {\n          w: nw,\n          p: POSTAG.D_A,\n        });\n        ie--;\n        continue;\n      }\n\n      // ============================================\n      // 数词组合\n      if ((w1.p & POSTAG.A_M) > 0) {\n        // debug(w2.w + ' ' + (w2.p & POSTAG.A_M));\n        // 百分比数字 如 10%，或者下一个词也是数词，则合并\n        if ((w2.p & POSTAG.A_M) > 0 || w2.w === '%') {\n          words.splice(i, 2, {\n            w: w1.w + w2.w,\n            p: POSTAG.A_M,\n          });\n          ie--;\n          continue;\n        }\n        // 数词 + 量词，合并。如： 100个\n        if ((w2.p & POSTAG.A_Q) > 0) {\n          words.splice(i, 2, {\n            w: w1.w + w2.w,\n            p: POSTAG.D_MQ, // 数量词\n          });\n          ie--;\n          continue;\n        }\n        // 带小数点的数字 ，如 “3 . 14”，或者 “十五点三”\n        // 数词 + \"分之\" + 数词，如“五十分之一”\n        var w3 = words[i + 2];\n        if (w3 && (w3.p & POSTAG.A_M) > 0 && (w2.w === '.' || w2.w === '点' || w2.w === '分之')) {\n          words.splice(i, 3, {\n            w: w1.w + w2.w + w3.w,\n            p: POSTAG.A_M,\n          });\n          ie -= 2;\n          continue;\n        }\n      }\n\n      // 修正 “十五点五八”问题\n      if ((w1.p & POSTAG.D_MQ) > 0 && w1.w.substr(-1) === '点' && w2.p & POSTAG.A_M) {\n        // debug(w1, w2);\n        let i2 = 2;\n        let w4w = '';\n        for (let j = i + i2; j < ie; j++) {\n          var w3 = words[j];\n          if ((w3.p & POSTAG.A_M) > 0) {\n            w4w += w3.w;\n            i2++;\n          } else {\n            break;\n          }\n        }\n        words.splice(i, i2, {\n          w: w1.w + w2.w + w4w,\n          p: POSTAG.D_MQ, // 数量词\n        });\n        ie -= i2 - 1;\n        continue;\n      }\n\n      // 移到下一个词\n      i++;\n    }\n\n    // 针对组合数字后无法识别新组合的数字问题，需要重新扫描一次\n    return isNotFirst === true ? words : this.doOptimize(words, true);\n  }\n}\n", "// @flow\nimport { Optimizer } from './BaseModule';\nimport type { SegmentToken } from './type';\n\n// 邮箱地址中允许出现的字符\n// 参考：http://www.cs.tut.fi/~jkorpela/rfc/822addr.html\nconst _EMAILCHAR = '!\"#$%&\\'*+-/0123456789=?@ABCDEFGHIJKLMNOPQRSTUVWXYZ^_`abcdefghijklmnopqrstuvwxyz{|}~.'.split('');\nconst EMAILCHAR = {};\nfor (const i in _EMAILCHAR) EMAILCHAR[_EMAILCHAR[i]] = 1;\n\nexport default class EmailOptimizer extends Optimizer {\n  doOptimize(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    // debug(words);\n\n    let i = 0;\n    let ie = words.length - 1;\n    let addr_start = false;\n    let has_at = false;\n    while (i < ie) {\n      var word = words[i];\n      var is_ascii = !!(word.p === POSTAG.A_NX || (word.p === POSTAG.A_M && word.w.charCodeAt(0) < 128));\n\n      // 如果是外文字符或者数字，符合电子邮件地址开头的条件\n      if (addr_start === false && is_ascii) {\n        addr_start = i;\n        i++;\n        continue;\n      } else {\n        // 如果遇到@符号，符合第二个条件\n        if (has_at === false && word.w === '@') {\n          has_at = true;\n          i++;\n          continue;\n        }\n        // 如果已经遇到过@符号，且出现了其他字符，则截取邮箱地址\n        if (has_at !== false && words[i - 1].w != '@' && is_ascii === false && !(word.w in EMAILCHAR)) {\n          var mailws = words.slice(addr_start, i);\n          words.splice(addr_start, mailws.length, {\n            w: EmailOptimizer.toEmailAddress(mailws),\n            p: POSTAG.URL,\n          });\n          i = addr_start + 1;\n          ie -= mailws.length - 1;\n          addr_start = false;\n          has_at = false;\n          continue;\n        }\n        // 如果已经开头\n        if (addr_start !== false && (is_ascii || word.w in EMAILCHAR)) {\n          i++;\n          continue;\n        }\n      }\n\n      // 移到下一个词\n      addr_start = false;\n      has_at = false;\n      i++;\n    }\n\n    // 检查剩余部分\n    if (addr_start && has_at && words[ie]) {\n      var word = words[ie];\n      var is_ascii = !!(word.p === POSTAG.A_NX || (word.p === POSTAG.A_M && word.w in EMAILCHAR));\n      if (is_ascii) {\n        var mailws = words.slice(addr_start, words.length);\n        words.splice(addr_start, mailws.length, {\n          w: EmailOptimizer.toEmailAddress(mailws),\n          p: POSTAG.URL,\n        });\n      }\n    }\n\n    return words;\n  }\n\n  /**\n * 根据一组单词生成邮箱地址\n *\n * @param {array} words 单词数组\n * @return {string}\n */\n  static toEmailAddress(words: Array<SegmentToken>): string {\n    let ret = words[0].w;\n    for (var i = 1, word; (word = words[i]); i++) {\n      ret += word.w;\n    }\n    return ret;\n  }\n}\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\n// 标点符号\nlet _STOPWORD = ' ,.;+-|/\\\\\\'\":?<>[]{}=!@#$%^&*()~`'\n  + '。，、＇：∶；?‘’“”〝〞ˆˇ﹕︰﹔﹖﹑·¨….¸;！´？！～—ˉ｜‖＂〃｀@﹫¡¿﹏﹋﹌︴々﹟#﹩$﹠&﹪%*﹡﹢﹦'\n  + '﹤‐￣¯―﹨ˆ˜﹍﹎+=<­＿_-ˇ~﹉﹊（）〈〉‹›﹛﹜『』〖〗［］《》〔〕{}「」【】︵︷︿︹︽_﹁﹃︻︶︸'\n  + '﹀︺︾ˉ﹂﹄︼＋－×÷﹢﹣±／＝≈≡≠∧∨∑∏∪∩∈⊙⌒⊥∥∠∽≌＜＞≤≥≮≯∧∨√﹙﹚[]﹛﹜∫∮∝∞⊙∏'\n  + '┌┬┐┏┳┓╒╤╕─│├┼┤┣╋┫╞╪╡━┃└┴┘┗┻┛╘╧╛┄┆┅┇╭─╮┏━┓╔╦╗┈┊│╳│┃┃╠╬╣┉┋╰─╯┗━┛'\n  + '╚╩╝╲╱┞┟┠┡┢┦┧┨┩┪╉╊┭┮┯┰┱┲┵┶┷┸╇╈┹┺┽┾┿╀╁╂╃╄╅╆'\n  + '○◇□△▽☆●◆■▲▼★♠♥♦♣☼☺◘♀√☻◙♂×▁▂▃▄▅▆▇█⊙◎۞卍卐╱╲▁▏↖↗↑←↔◤◥╲╱▔▕↙↘↓→↕◣◢∷▒░℡™';\n_STOPWORD = _STOPWORD.split('');\nconst STOPWORD = {};\nconst STOPWORD2 = {};\nfor (const i in _STOPWORD) {\n  if (_STOPWORD[i] === '') continue;\n  const len = _STOPWORD[i].length;\n  STOPWORD[_STOPWORD[i]] = len;\n  if (!STOPWORD2[len]) STOPWORD2[len] = {};\n  STOPWORD2[len][_STOPWORD[i]] = len;\n}\n\nexport default class PunctuationTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    const ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p > 0) {\n        ret.push(word);\n        continue;\n      }\n      // 仅对未识别的词进行匹配\n      const stopinfo = PunctuationTokenizer.matchStopword(word.w);\n      if (stopinfo.length < 1) {\n        ret.push(word);\n        continue;\n      }\n      // 分离出标点符号\n      let lastc = 0;\n      for (var ui = 0, sw; (sw = stopinfo[ui]); ui++) {\n        if (sw.c > lastc) {\n          ret.push({ w: word.w.substr(lastc, sw.c - lastc) });\n        }\n        // 忽略空格\n        if (sw.w != ' ') {\n          ret.push({ w: sw.w, p: POSTAG.D_W });\n        }\n        lastc = sw.c + sw.w.length;\n      }\n      const lastsw = stopinfo[stopinfo.length - 1];\n      if (lastsw.c + lastsw.w.length < word.w.length) {\n        ret.push({ w: word.w.substr(lastsw.c + lastsw.w.length) });\n      }\n    }\n    return ret;\n  }\n\n  /**\n   * 匹配包含的标点符号，返回相关信息\n   *\n   * @param {string} text 文本\n   * @param {int} cur 开始位置\n   * @return {array}  返回格式   {w: '网址', c: 开始位置}\n   */\n  static matchStopword(text: string, cur: number): Array<TokenStartPosition> {\n    if (isNaN(cur)) cur = 0;\n    const ret = [];\n    let isMatch = false;\n    while (cur < text.length) {\n      for (const i in STOPWORD2) {\n        var w = text.substr(cur, i);\n        if (w in STOPWORD2[i]) {\n          ret.push({ w, c: cur });\n          isMatch = true;\n          break;\n        }\n      }\n      cur += isMatch === false ? 1 : w.length;\n      isMatch = false;\n    }\n\n    return ret;\n  }\n}\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\n// 协议URL头\nconst PROTOTAL = ['http://', 'https://', 'ftp://', 'news://', 'telnet://'];\n// 协议头最小长度\nlet MIN_PROTOTAL_LEN = 100;\nfor (var i in PROTOTAL) {\n  if (PROTOTAL[i].length < MIN_PROTOTAL_LEN) {\n    MIN_PROTOTAL_LEN = PROTOTAL[i].length;\n  }\n}\n// 允许出现在URL中的字符\nconst _URLCHAR = [\n  'a',\n  'b',\n  'c',\n  'd',\n  'e',\n  'f',\n  'g',\n  'h',\n  'i',\n  'j',\n  'k',\n  'l',\n  'm',\n  'n',\n  'o',\n  'p',\n  'q',\n  'r',\n  's',\n  't',\n  'u',\n  'v',\n  'w',\n  'x',\n  'y',\n  'z',\n  'A',\n  'B',\n  'C',\n  'D',\n  'E',\n  'F',\n  'G',\n  'H',\n  'I',\n  'J',\n  'K',\n  'L',\n  'M',\n  'N',\n  'O',\n  'P',\n  'Q',\n  'R',\n  'S',\n  'T',\n  'U',\n  'V',\n  'W',\n  'X',\n  'Y',\n  'Z',\n  '0',\n  '1',\n  '2',\n  '3',\n  '4',\n  '5',\n  '6',\n  '7',\n  '8',\n  '9',\n  '!',\n  '#',\n  '$',\n  '%',\n  '&',\n  '‘',\n  '(',\n  ')',\n  '*',\n  '+',\n  ',',\n  '-',\n  '.',\n  '/',\n  ':',\n  ';',\n  '=',\n  '?',\n  '@',\n  '[',\n  '\\\\',\n  ']',\n  '^',\n  '_',\n  '`',\n  '|',\n  '~',\n];\nconst URLCHAR = {};\nfor (var i in _URLCHAR) {\n  URLCHAR[_URLCHAR[i]] = 1;\n}\n\nexport default class URLTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    const ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p > 0) {\n        ret.push(word);\n        continue;\n      }\n      // 仅对未识别的词进行匹配\n      const urlinfo = URLTokenizer.matchURL(word.w);\n      if (urlinfo.length < 1) {\n        ret.push(word);\n        continue;\n      }\n      // 分离出URL\n      let lastc = 0;\n      for (var ui = 0, url; (url = urlinfo[ui]); ui++) {\n        if (url.c > lastc) {\n          ret.push({ w: word.w.substr(lastc, url.c - lastc) });\n        }\n        ret.push({ w: url.w, p: POSTAG.URL });\n        lastc = url.c + url.w.length;\n      }\n      const lasturl = urlinfo[urlinfo.length - 1];\n      if (lasturl.c + lasturl.w.length < word.w.length) {\n        ret.push({ w: word.w.substr(lasturl.c + lasturl.w.length) });\n      }\n    }\n    // debug(ret);\n    return ret;\n  }\n\n  /**\n   * 匹配包含的网址，返回相关信息\n   *\n   * @param {string} text 文本\n   * @param {int} cur 开始位置\n   * @return {array}  返回格式   {w: '网址', c: 开始位置}\n   */\n  static matchURL(text: string, cur: number): Array<TokenStartPosition> {\n    if (isNaN(cur)) cur = 0;\n    const ret = [];\n    let s = false;\n    while (cur < text.length) {\n      // 判断是否为 http:// 之类的文本开头\n      if (s === false && cur < text.length - MIN_PROTOTAL_LEN) {\n        for (var i = 0, prot; (prot = PROTOTAL[i]); i++) {\n          if (text.substr(cur, prot.length) === prot) {\n            s = cur;\n            cur += prot.length - 1;\n            break;\n          }\n        }\n      } else if (s !== false && !(text.charAt(cur) in URLCHAR)) {\n        // 如果以http://之类开头，遇到了非URL字符，则结束\n        ret.push({\n          w: text.substr(s, cur - s),\n          c: s,\n        });\n        s = false;\n      }\n      cur++;\n    }\n    // 检查剩余部分\n    if (s !== false) {\n      ret.push({\n        w: text.substr(s, cur - s),\n        c: s,\n      });\n    }\n\n    return ret;\n  }\n}\n", "// @flow\nimport { Optimizer } from './BaseModule';\nimport type { SegmentToken } from './type';\n\nimport {\n  FAMILY_NAME_1, FAMILY_NAME_2, SINGLE_NAME, DOUBLE_NAME_1, DOUBLE_NAME_2,\n} from './CHS_NAMES';\n\nexport default class ChsNameOptimizer extends Optimizer {\n  doOptimize(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    let i = 0;\n\n    /* 第一遍扫描 */\n    while (i < words.length) {\n      const word = words[i];\n      const nextword = words[i + 1];\n      if (nextword) {\n        // debug(nextword);\n        // 如果为  \"小|老\" + 姓\n        if (\n          nextword\n          && (word.w === '小' || word.w === '老')\n          && (nextword.w in FAMILY_NAME_1 || nextword.w in FAMILY_NAME_2)\n        ) {\n          words.splice(i, 2, {\n            w: word.w + nextword.w,\n            p: POSTAG.A_NR,\n          });\n          i++;\n          continue;\n        }\n\n        // 如果是 姓 + 名（2字以内）\n        if (\n          (word.w in FAMILY_NAME_1 || word.w in FAMILY_NAME_2)\n          && ((nextword.p & POSTAG.A_NR) > 0 && nextword.w.length <= 2)\n        ) {\n          words.splice(i, 2, {\n            w: word.w + nextword.w,\n            p: POSTAG.A_NR,\n          });\n          i++;\n          continue;\n        }\n\n        // 如果相邻两个均为单字且至少有一个字是未识别的，则尝试判断其是否为人名\n        if (!word.p || !nextword.p) {\n          if (\n            (word.w in SINGLE_NAME && word.w === nextword.w)\n            || (word.w in DOUBLE_NAME_1 && nextword.w in DOUBLE_NAME_2)\n          ) {\n            words.splice(i, 2, {\n              w: word.w + nextword.w,\n              p: POSTAG.A_NR,\n            });\n            // 如果上一个单词可能是一个姓，则合并\n            const preword = words[i - 1];\n            if (preword && (preword.w in FAMILY_NAME_1 || preword.w in FAMILY_NAME_2)) {\n              words.splice(i - 1, 2, {\n                w: preword.w + word.w + nextword.w,\n                p: POSTAG.A_NR,\n              });\n            } else {\n              i++;\n            }\n            continue;\n          }\n        }\n\n        // 如果为 无歧义的姓 + 名（2字以内） 且其中一个未未识别词\n        if ((word.w in FAMILY_NAME_1 || word.w in FAMILY_NAME_2) && (!word.p || !nextword.p)) {\n          // debug(word, nextword);\n          words.splice(i, 2, {\n            w: word.w + nextword.w,\n            p: POSTAG.A_NR,\n          });\n        }\n      }\n\n      // 移到下一个单词\n      i++;\n    }\n\n    /* 第二遍扫描 */\n    i = 0;\n    while (i < words.length) {\n      const word = words[i];\n      const nextword = words[i + 1];\n      if (nextword) {\n        // 如果为 姓 + 单字名\n        if ((word.w in FAMILY_NAME_1 || word.w in FAMILY_NAME_2) && nextword.w in SINGLE_NAME) {\n          words.splice(i, 2, {\n            w: word.w + nextword.w,\n            p: POSTAG.A_NR,\n          });\n          i++;\n          continue;\n        }\n      }\n\n      // 移到下一个单词\n      i++;\n    }\n\n    return words;\n  }\n}\n", "// @flow\nimport { Optimizer } from './BaseModule';\nimport type { SegmentToken } from './type';\n\n// 日期时间常见组合\nconst DATETIME_WORDS = ['世纪', '年', '年份', '年度', '月', '月份', '月度', '日', '号', '时', '点', '点钟', '分', '分钟', '秒', '毫秒'];\nconst DATETIME = {};\n// eslint-disable-next-line\nfor (const i in DATETIME_WORDS) {\n  DATETIME[DATETIME_WORDS[i]] = DATETIME_WORDS[i].length;\n}\n\nexport default class DatetimeOptimizer extends Optimizer {\n  /**\n   * 日期时间优化\n   *\n   * @param {array} words 单词数组\n   * @param {bool} isNotFirst 是否为管理器调用的\n   * @return {array}\n   */\n  doOptimize(words: Array<SegmentToken>, isNotFirst: boolean): Array<SegmentToken> {\n    if (typeof isNotFirst === 'undefined') {\n      isNotFirst = false;\n    }\n    // 合并相邻的能组成一个单词的两个词\n    const TABLE = this.segment.getDict('TABLE');\n    const POSTAG = this.segment.POSTAG;\n\n    let i = 0;\n    let ie = words.length - 1;\n    while (i < ie) {\n      var w1 = words[i];\n      var w2 = words[i + 1];\n      // debug(w1.w + ', ' + w2.w);\n\n      if ((w1.p & POSTAG.A_M) > 0) {\n        // =========================================\n        // 日期时间组合   数字 + 日期单位，如 “2005年\"\n        if (w2.w in DATETIME) {\n          let nw = w1.w + w2.w;\n          let len = 2;\n          // 继续搜索后面连续的日期时间描述，必须符合  数字 + 日期单位\n          while (true) {\n            var w1 = words[i + len];\n            var w2 = words[i + len + 1];\n            if (w1 && w2 && (w1.p & POSTAG.A_M) > 0 && w2.w in DATETIME) {\n              len += 2;\n              nw += w1.w + w2.w;\n            } else {\n              break;\n            }\n          }\n          words.splice(i, len, {\n            w: nw,\n            p: POSTAG.D_T,\n          });\n          ie -= len - 1;\n          continue;\n        }\n        // =========================================\n      }\n\n      // 移到下一个词\n      i++;\n    }\n\n    return words;\n  }\n}\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport { FAMILY_NAME_1, FAMILY_NAME_2 } from './CHS_NAMES';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\n// 日期时间常见组合\nconst _DATETIME = ['世纪', '年', '年份', '年度', '月', '月份', '月度', '日', '号', '时', '点', '点钟', '分', '分钟', '秒', '毫秒'];\nconst DATETIME = {};\nfor (const i in _DATETIME) DATETIME[_DATETIME[i]] = _DATETIME[i].length;\n\n/**\n * 对未识别的单词进行分词\n *\n * @param {array} words 单词数组\n * @return {array}\n */\nexport default class DictTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    // debug(words);\n    const POSTAG = this.segment.POSTAG;\n    const TABLE = this.segment.getDict('TABLE');\n    const ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p > 0) {\n        ret.push(word);\n        continue;\n      }\n      // 仅对未识别的词进行匹配\n      const wordinfo = this.matchWord(word.w, 0, words[i - 1]);\n      if (wordinfo.length < 1) {\n        ret.push(word);\n        continue;\n      }\n      // 分离出已识别的单词\n      let lastc = 0;\n      for (var ui = 0, bw; (bw = wordinfo[ui]); ui++) {\n        if (bw.c > lastc) {\n          ret.push({ w: word.w.substr(lastc, bw.c - lastc) });\n        }\n        ret.push({ w: bw.w, p: TABLE[bw.w].p });\n        lastc = bw.c + bw.w.length;\n      }\n      const lastword = wordinfo[wordinfo.length - 1];\n      if (lastword.c + lastword.w.length < word.w.length) {\n        ret.push({ w: word.w.substr(lastword.c + lastword.w.length) });\n      }\n    }\n    return ret;\n  }\n\n  /**\n   * 匹配单词，返回相关信息\n   *\n   * @param {string} text 文本\n   * @param {int} cur 开始位置\n   * @param {object} preword 上一个单词\n   * @return {array}  返回格式   {w: '单词', c: 开始位置}\n   */\n  matchWord(text, cur, preword): Array<TokenStartPosition> {\n    if (isNaN(cur)) cur = 0;\n    const ret = [];\n    const s = false;\n    const TABLE = this.segment.getDict('TABLE2');\n    // 匹配可能出现的单词\n    while (cur < text.length) {\n      for (const i in TABLE) {\n        const w = text.substr(cur, i);\n        if (w in TABLE[i]) {\n          ret.push({ w, c: cur, f: TABLE[i][w].f });\n        }\n      }\n      cur++;\n    }\n\n    return this.filterWord(ret, preword, text);\n  }\n  // debug(matchWord('长春市长春药店'));\n\n  /**\n   * 选择最有可能匹配的单词\n   *\n   * @param {array} words 单词信息数组\n   * @param {object} preword 上一个单词\n   * @param {string} text 本节要分词的文本\n   * @return {array}\n   */\n  filterWord(words, preword, text) {\n    const POSTAG = this.segment.POSTAG;\n    const TABLE = this.segment.getDict('TABLE');\n    let ret = [];\n\n    // 将单词按位置分组\n    const wordpos = DictTokenizer.getPosInfo(words, text);\n    // debug(wordpos);\n\n    // 使用类似于MMSG的分词算法\n    // 找出所有分词可能，主要根据一下几项来评价：\n    // x、词数量最少；\n    // a、词平均频率最大；\n    // b、每个词长度标准差最小；\n    // c、未识别词最少；\n    // d、符合语法结构项：如两个连续的动词减分，数词后面跟量词加分；\n    // 取以上几项综合排名最最好的\n    const chunks = DictTokenizer.getChunks(wordpos, 0, text);\n    // debug(chunks);\n    const assess = []; // 评价表\n\n    // 对各个分支就行评估\n    for (var i = 0, chunk; (chunk = chunks[i]); i++) {\n      assess[i] = {\n        x: chunk.length,\n        a: 0,\n        b: 0,\n        c: 0,\n        d: 0,\n      };\n      // 词平均长度\n      const sp = text.length / chunk.length;\n      // 句子经常包含的语法结构\n      let has_D_V = false; // 是否包含动词\n\n      // 遍历各个词\n      if (preword) {\n        var prew = { w: preword.w, p: preword.p, f: preword.f };\n      } else {\n        prew = false;\n      }\n      for (var j = 0, w; (w = chunk[j]); j++) {\n        if (w.w in TABLE) {\n          w.p = TABLE[w.w].p;\n          assess[i].a += w.f; // 总词频\n\n          // ================ 检查语法结构 ===================\n          if (prew) {\n            // 如果上一个词是数词且当前词是量词（单位），则加分\n            if ((prew.p & POSTAG.A_M) > 0 && ((TABLE[w.w].p & POSTAG.A_Q) > 0 || w.w in DATETIME)) {\n              assess[i].d++;\n            }\n            // 如果当前词是动词\n            if ((w.p & POSTAG.D_V) > 0) {\n              has_D_V = true;\n              // 如果是连续的两个动词，则减分\n              // if ((prew.p & POSTAG.D_V) > 0)\n              // assess[i].d--;\n              // 如果是 形容词 + 动词，则加分\n              if ((prew.p & POSTAG.D_A) > 0) {\n                assess[i].d++;\n              }\n            }\n            // 如果是地区名、机构名或形容词，后面跟地区、机构、代词、名词等，则加分\n            if (\n              ((prew.p & POSTAG.A_NS) > 0 || prew.p & POSTAG.A_NT || (prew.p & POSTAG.D_A) > 0)\n              && ((w.p & POSTAG.D_N) > 0\n                || (w.p & POSTAG.A_NR) > 0\n                || (w.p & POSTAG.A_NS) > 0\n                || (w.p & POSTAG.A_NZ) > 0\n                || (w.p & POSTAG.A_NT) > 0)\n            ) {\n              assess[i].d++;\n            }\n            // 如果是 方位词 + 数量词，则加分\n            if ((prew.p & POSTAG.D_F) > 0 && (w.p & (POSTAG.A_M > 0) || w.p & (POSTAG.D_MQ > 0))) {\n              // debug(prew, w);\n              assess[i].d++;\n            }\n            // 如果是 姓 + 名词，则加分\n            if (\n              (prew.w in FAMILY_NAME_1 || prew.w in FAMILY_NAME_2)\n              && ((w.p & POSTAG.D_N) > 0 || (w.p & POSTAG.A_NZ) > 0)\n            ) {\n              // debug(prew, w);\n              assess[i].d++;\n            }\n\n            // 探测下一个词\n            const nextw = chunk[j + 1];\n            if (nextw) {\n              if (nextw.w in TABLE) {\n                nextw.p = TABLE[nextw.w].p;\n              }\n              // 如果是连词，前后两个词词性相同则加分\n              if ((w.p & POSTAG.D_C) > 0 && prew.p === nextw.p) {\n                assess[i].d++;\n              }\n              // 如果当前是“的”+ 名词，则加分\n              if (\n                (w.w === '的' || w.w === '之')\n                && ((nextw.p & POSTAG.D_N) > 0\n                  || (nextw.p & POSTAG.A_NR) > 0\n                  || (nextw.p & POSTAG.A_NS) > 0\n                  || (nextw.p & POSTAG.A_NZ) > 0\n                  || (nextw.p & POSTAG.A_NT) > 0)\n              ) {\n                assess[i].d += 1.5;\n              }\n            }\n          }\n          // ===========================================\n        } else {\n          assess[i].c++; // 未识别的词数量\n        }\n        // 标准差\n        assess[i].b += Math.pow(sp - w.w.length, 2);\n        prew = chunk[j];\n      }\n      // 如果句子中包含了至少一个动词\n      if (has_D_V === false) assess[i].d -= 0.5;\n\n      assess[i].a = assess[i].a / chunk.length;\n      assess[i].b = assess[i].b / chunk.length;\n    }\n\n    // 计算排名\n    const top = DictTokenizer.getTops(assess);\n    const currchunk = chunks[top];\n\n    // 剔除不能识别的词\n    for (var i = 0, word; (word = currchunk[i]); i++) {\n      if (!(word.w in TABLE)) {\n        currchunk.splice(i--, 1);\n      }\n    }\n    ret = currchunk;\n\n    // debug(ret);\n    return ret;\n  }\n\n  /* 将单词按照位置排列\n    *\n    * @param {array} words\n    * @param {string} text\n    * @return {object}\n    */\n  static getPosInfo(words, text) {\n    const wordpos = {};\n    // 将单词按位置分组\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (!wordpos[word.c]) {\n        wordpos[word.c] = [];\n      }\n      wordpos[word.c].push(word);\n    }\n    // 按单字分割文本，填补空缺的位置\n    for (var i = 0; i < text.length; i++) {\n      if (!wordpos[i]) {\n        wordpos[i] = [{ w: text.charAt(i), c: i, f: 0 }];\n      }\n    }\n\n    return wordpos;\n  }\n\n  /**\n    * 取所有分支\n    *\n    * @param {object} wordpos\n    * @param {int} pos 当前位置\n    * @param {string} text 本节要分词的文本\n    * @return {array}\n    */\n  static getChunks(wordpos, pos, text) {\n    const words = wordpos[pos] || [];\n    const ret = [];\n    for (let i = 0; i < words.length; i++) {\n      const word = words[i];\n      // debug(word);\n      const nextcur = word.c + word.w.length;\n      if (!wordpos[nextcur]) {\n        ret.push([word]);\n      } else {\n        const chunks = DictTokenizer.getChunks(wordpos, nextcur);\n        for (let j = 0; j < chunks.length; j++) {\n          ret.push([word].concat(chunks[j]));\n        }\n      }\n    }\n    return ret;\n  }\n\n  /**\n    * 评价排名\n    *\n    * @param {object} assess\n    * @return {object}\n    */\n  static getTops(assess) {\n    // 取各项最大值\n    const top = {\n      x: assess[0].x,\n      a: assess[0].a,\n      b: assess[0].b,\n      c: assess[0].c,\n      d: assess[0].d,\n    };\n    for (var i = 1, ass; (ass = assess[i]); i++) {\n      if (ass.a > top.a) top.a = ass.a; // 取最大平均词频\n      if (ass.b < top.b) top.b = ass.b; // 取最小标准差\n      if (ass.c > top.c) top.c = ass.c; // 取最大未识别词\n      if (ass.d < top.d) top.d = ass.d; // 取最小语法分数\n      if (ass.x > top.x) top.x = ass.x; // 取最大单词数量\n    }\n    // debug(top);\n\n    // 评估排名\n    const tops = [];\n    for (var i = 0, ass; (ass = assess[i]); i++) {\n      tops[i] = 0;\n      // 词数量，越小越好\n      tops[i] += (top.x - ass.x) * 1.5;\n      // 词总频率，越大越好\n      if (ass.a >= top.a) tops[i] += 1;\n      // 词标准差，越小越好\n      if (ass.b <= top.b) tops[i] += 1;\n      // 未识别词，越小越好\n      tops[i] += top.c - ass.c; // debug(tops[i]);\n      // 符合语法结构程度，越大越好\n      tops[i] += (ass.d < 0 ? top.d + ass.d : ass.d - top.d) * 1;\n      // debug(tops[i]);debug('---');\n    }\n    // debug(tops.join('  '));\n\n    // 取分数最高的\n    let curri = 0;\n    let maxs = tops[0];\n    for (var i in tops) {\n      const s = tops[i];\n      if (s > maxs) {\n        curri = i;\n        maxs = s;\n      } else if (s === maxs) {\n        // 如果分数相同，则根据词长度、未识别词个数和平均频率来选择\n        let a = 0;\n        let b = 0;\n        if (assess[i].c < assess[curri].c) a++;\n        else b++;\n        if (assess[i].a > assess[curri].a) a++;\n        else b++;\n        if (assess[i].x < assess[curri].x) a++;\n        else b++;\n        if (a > b) {\n          curri = i;\n          maxs = s;\n        }\n      }\n    }\n    return curri;\n  }\n}\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\nexport default class ForeignTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    let ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p) {\n        ret.push(word);\n      } else {\n        // 仅对未识别的词进行匹配\n        ret = ret.concat(this.splitForeign(word.w));\n      }\n    }\n    return ret;\n  }\n\n  /**\n   * 匹配包含的英文字符和数字，并分割\n   *\n   * @param {string} text 文本\n   * @param {int} cur 开始位置\n   * @return {array}  返回格式   {w: '单词', c: 开始位置}\n   */\n  splitForeign(text: string, cur: number): Array<TokenStartPosition> {\n    const POSTAG = this.segment.POSTAG;\n    if (isNaN(cur)) cur = 0;\n    const ret = [];\n\n    // 取第一个字符的ASCII码\n    let lastcur = 0;\n    let lasttype = 0;\n    var c = text.charCodeAt(0);\n    // 全角数字或字母\n    if (c >= 65296 && c <= 65370) c -= 65248;\n    // 数字  lasttype = POSTAG.A_M\n    if (c >= 48 && c <= 57) lasttype = POSTAG.A_M;\n    else if ((c >= 65 && c <= 90) || (c >= 97 && c <= 122)) {\n      // 字母 lasttype = POSTAG.A_NX\n      lasttype = POSTAG.A_NX;\n    } else lasttype = POSTAG.UNK;\n\n    for (var i = 1; i < text.length; i++) {\n      var c = text.charCodeAt(i);\n      // 全角数字或字母\n      if (c >= 65296 && c <= 65370) c -= 65248;\n      // 数字  lasttype = POSTAG.A_M\n      if (c >= 48 && c <= 57) {\n        if (lasttype !== POSTAG.A_M) {\n          var nw = { w: text.substr(lastcur, i - lastcur) };\n          if (lasttype !== POSTAG.UNK) nw.p = lasttype;\n          ret.push(nw);\n          lastcur = i;\n        }\n        lasttype = POSTAG.A_M;\n      } else if ((c >= 65 && c <= 90) || (c >= 97 && c <= 122)) {\n        // 字母 lasttype = POSTAG.A_NX\n        if (lasttype !== POSTAG.A_NX) {\n          var nw = { w: text.substr(lastcur, i - lastcur) };\n          if (lasttype !== POSTAG.UNK) nw.p = lasttype;\n          ret.push(nw);\n          lastcur = i;\n        }\n        lasttype = POSTAG.A_NX;\n      } else {\n        // 其他\n        if (lasttype !== POSTAG.UNK) {\n          ret.push({\n            w: text.substr(lastcur, i - lastcur),\n            p: [lasttype],\n          });\n          lastcur = i;\n        }\n        lasttype = POSTAG.UNK;\n      }\n    }\n    // 剩余部分\n    var nw = { w: text.substr(lastcur, i - lastcur) };\n    if (lasttype !== POSTAG.UNK) nw.p = lasttype;\n    ret.push(nw);\n\n    // debug(ret);\n    return ret;\n  }\n}\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\n// 单字切分模块\nexport default class SingleTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    let ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p) {\n        ret.push(word);\n      } else {\n        // 仅对未识别的词进行匹配\n        ret = ret.concat(this.splitSingle(word.w));\n      }\n    }\n    return ret;\n  }\n\n  /**\n   * 单字切分\n   *\n   * @param {string} text 要切分的文本\n   * @param {int} cur 开始位置\n   * @return {array}\n   */\n  splitSingle(text: string, cur: number): Array<TokenStartPosition> {\n    const POSTAG = this.segment.POSTAG;\n    if (isNaN(cur)) cur = 0;\n    const ret = [];\n    while (cur < text.length) {\n      ret.push({\n        w: text.charAt(cur),\n        p: POSTAG.UNK,\n      });\n      cur++;\n    }\n    return ret;\n  }\n}\n", "// @flow\nimport { Tokenizer } from './BaseModule';\nimport type { SegmentToken, TokenStartPosition } from './type';\n\n// 通配符识别模块\nexport default class WildcardTokenizer extends Tokenizer {\n  split(words: Array<SegmentToken>): Array<SegmentToken> {\n    const POSTAG = this.segment.POSTAG;\n    const TABLE = this.segment.getDict('WILDCARD');\n    const ret = [];\n    for (var i = 0, word; (word = words[i]); i++) {\n      if (word.p > 0) {\n        ret.push(word);\n        continue;\n      }\n      // 仅对未识别的词进行匹配\n      const wordinfo = this.matchWord(word.w);\n      if (wordinfo.length < 1) {\n        ret.push(word);\n        continue;\n      }\n      // 分离出已识别的单词\n      let lastc = 0;\n      for (var ui = 0, bw; (bw = wordinfo[ui]); ui++) {\n        if (bw.c > lastc) {\n          ret.push({ w: word.w.substr(lastc, bw.c - lastc) });\n        }\n        ret.push({ w: bw.w, p: TABLE[bw.w.toLowerCase()].p });\n        lastc = bw.c + bw.w.length;\n      }\n      const lastword = wordinfo[wordinfo.length - 1];\n      if (lastword.c + lastword.w.length < word.w.length) {\n        ret.push({ w: word.w.substr(lastword.c + lastword.w.length) });\n      }\n    }\n    return ret;\n  }\n\n  /**\n   * 匹配单词，返回相关信息\n   *\n   * @param {string} text 文本\n   * @param {int} cur 开始位置\n   * @return {array}  返回格式   {w: '单词', c: 开始位置}\n   */\n  matchWord(text: string, cur: number): Array<TokenStartPosition> {\n    if (isNaN(cur)) cur = 0;\n    const ret = [];\n    const s = false;\n    const TABLE = this.segment.getDict('WILDCARD2');\n    // 匹配可能出现的单词，取长度最大的那个\n    const lowertext = text.toLowerCase();\n    while (cur < text.length) {\n      let stopword = false;\n      for (const i in TABLE) {\n        if (lowertext.substr(cur, i) in TABLE[i]) {\n          stopword = { w: text.substr(cur, i), c: cur };\n        }\n      }\n      if (stopword !== false) {\n        ret.push(stopword);\n        cur += stopword.w.length;\n      } else {\n        cur++;\n      }\n    }\n    return ret;\n  }\n}\n", "// @flow\nimport AdjectiveOptimizer from './AdjectiveOptimizer';\nimport CHS_NAMES from './CHS_NAMES';\nimport ChsNameTokenizer from './ChsNameTokenizer';\nimport DictOptimizer from './DictOptimizer';\nimport EmailOptimizer from './EmailOptimizer';\nimport PunctuationTokenizer from './PunctuationTokenizer';\nimport URLTokenizer from './URLTokenizer';\nimport ChsNameOptimizer from './ChsNameOptimizer';\nimport DatetimeOptimizer from './DatetimeOptimizer';\nimport DictTokenizer from './DictTokenizer';\nimport ForeignTokenizer from './ForeignTokenizer';\nimport SingleTokenizer from './SingleTokenizer';\nimport WildcardTokenizer from './WildcardTokenizer';\n\nexport {\n  AdjectiveOptimizer,\n  CHS_NAMES,\n  ChsNameTokenizer,\n  DictOptimizer,\n  EmailOptimizer,\n  PunctuationTokenizer,\n  URLTokenizer,\n  ChsNameOptimizer,\n  DatetimeOptimizer,\n  DictTokenizer,\n  ForeignTokenizer,\n  SingleTokenizer,\n  WildcardTokenizer,\n};\n\nexport const modules = [\n  // 强制分割类单词识别\n  URLTokenizer, // URL识别\n  WildcardTokenizer, // 通配符，必须在标点符号识别之前\n  PunctuationTokenizer, // 标点符号识别\n  ForeignTokenizer, // 外文字符、数字识别，必须在标点符号识别之后\n  // 中文单词识别\n  DictTokenizer, // 词典识别\n  ChsNameTokenizer, // 人名识别，建议在词典识别之后\n  // 优化模块\n  EmailOptimizer, // 邮箱地址识别\n  ChsNameOptimizer, // 人名识别优化\n  DictOptimizer, // 词典识别优化\n  DatetimeOptimizer, // 日期时间识别优化\n  AdjectiveOptimizer,\n];\n\nexport { Module, Tokenizer, Optimizer } from './BaseModule';\nexport type { SegmentToken, TokenStartPosition } from './type';\n", "// @flow\nimport preval from 'preval.macro';\n\nconst importExport = `\n  const fs = require('fs');\n  const path = require('path');\n  module.exports = fs.readFileSync(path.join(__dirname, '`;\nconst tail = \".txt'), 'utf8')\";\n\n// 盘古词典\nexport const pangu: string = preval`${importExport}pangu${tail}`;\n// 扩展词典（用于调整原盘古词典）\nexport const panguExtend1: string = preval`${importExport}panguExtend1${tail}`;\nexport const panguExtend2: string = preval`${importExport}panguExtend2${tail}`;\n// 常见名词、人名\nexport const names: string = preval`${importExport}names${tail}`;\n// 通配符\nexport const wildcard: string = preval`${importExport}wildcard${tail}`;\n// 同义词\nexport const synonym: string = preval`${importExport}synonym${tail}`;\n// 停止符\nexport const stopword: string = preval`${importExport}stopword${tail}`;\n\n// 字典集，方便 import\nexport const dicts: string[] = [pangu, panguExtend1, panguExtend2, names, wildcard];\nexport const synonyms: string[] = [synonym];\nexport const stopwords: string[] = [stopword];\n", "// @flow\nimport Segment from './Segment';\n\nimport {\n  modules,\n  Module,\n  Tokenizer,\n  Optimizer,\n  CHS_NAMES,\n  ChsNameTokenizer,\n  DictOptimizer,\n  EmailOptimizer,\n  PunctuationTokenizer,\n  URLTokenizer,\n  ChsNameOptimizer,\n  DatetimeOptimizer,\n  DictTokenizer,\n  ForeignTokenizer,\n  SingleTokenizer,\n  WildcardTokenizer,\n} from './module';\n\nimport POSTAG, { getPOSTagTranslator, cnPOSTag, enPOSTag } from './POSTAG';\n\nimport {\n  pangu,\n  panguExtend1,\n  panguExtend2,\n  names,\n  wildcard,\n  synonym,\n  stopword,\n  dicts,\n  synonyms,\n  stopwords,\n} from './knowledge';\n\nexport type { SegmentToken, TokenStartPosition } from './module';\n\nexport function useDefault(segmentInstance: Segment): Segment {\n  segmentInstance.use(modules);\n  segmentInstance.loadDict(dicts);\n  segmentInstance.loadSynonymDict(synonyms);\n  segmentInstance.loadStopwordDict(stopwords);\n  return segmentInstance;\n}\n\nexport {\n  modules,\n  Module,\n  Tokenizer,\n  Optimizer,\n  CHS_NAMES,\n  ChsNameTokenizer,\n  DictOptimizer,\n  EmailOptimizer,\n  PunctuationTokenizer,\n  URLTokenizer,\n  ChsNameOptimizer,\n  DatetimeOptimizer,\n  DictTokenizer,\n  ForeignTokenizer,\n  SingleTokenizer,\n  WildcardTokenizer,\n};\n\nexport {\n  POSTAG, getPOSTagTranslator, cnPOSTag, enPOSTag,\n};\n\nexport {\n  pangu,\n  panguExtend1,\n  panguExtend2,\n  names,\n  wildcard,\n  synonym,\n  stopword,\n  dicts,\n  synonyms,\n  stopwords,\n};\n\nexport { Segment };\n"], "names": ["POSTAG", "D_A", "D_B", "D_C", "D_D", "D_E", "D_F", "D_I", "D_L", "A_M", "D_MQ", "D_N", "D_O", "D_P", "A_Q", "D_R", "D_S", "D_T", "D_U", "D_V", "D_W", "D_X", "D_Y", "D_Z", "A_NR", "A_NS", "A_NT", "A_NX", "A_NZ", "D_ZH", "D_K", "UNK", "URL", "CN_POS_NAMES", "EN_POS_NAMES", "getPOSTagTranslator", "POSTagDict", "I18NDict", "posTagNumber", "isNaN", "result", "key", "push", "length", "cnPOSTag", "enPOSTag", "Tokenizer", "segment", "text", "modules", "Error", "w", "for<PERSON>ach", "module", "split", "Optimizer", "words", "doOptimize", "Segment", "<PERSON><PERSON><PERSON>", "Array", "isArray", "_this", "use", "init", "type", "dict", "convertToLower", "d", "loadDict", "DICT", "TABLE", "TABLE2", "map", "line", "toLowerCase", "blocks", "trim", "f", "p", "loadSynonymDict", "n1", "n2", "loadStopwordDict", "options", "me", "ret", "convertSynonym", "list", "count", "getDict", "item", "replace", "section", "sret", "tokenizer", "optimizer", "concat", "stripPunctuation", "filter", "stripStopword", "STOPWORD", "simple", "this", "join", "s", "lasti", "i", "slice", "cur", "COLOR_WITH_RGB", "colors", "AdjectiveOptimizer", "index", "word", "nextword", "includes", "isNominal", "pos", "addOrderInfo", "chars", "order", "char", "FAMILY_NAME_1", "FAMILY_NAME_2", "DOUBLE_NAME_1", "DOUBLE_NAME_2", "SINGLE_NAME", "ChsNameTokenizer", "nameinfo", "matchName", "url", "lastc", "ui", "c", "substr", "lastn", "startPos", "startPosition", "name", "f2", "f1", "DictOptimizer", "isNotFirst", "ie", "w1", "w2", "nw", "splice", "w3", "i2", "w4w", "j", "_EMAILCHAR", "EMAILCHAR", "EmailOptimizer", "addr_start", "has_at", "is_ascii", "charCodeAt", "mailws", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_STOPWORD", "STOPWORD2", "len", "PunctuationTokenizer", "stopinfo", "matchStopword", "sw", "<PERSON>w", "isMatch", "PROTOTAL", "MIN_PROTOTAL_LEN", "_URLCHAR", "URLCHAR", "URLTokenizer", "u<PERSON><PERSON>", "matchURL", "lasturl", "prot", "ChsNameOptimizer", "preword", "DATETIME_WORDS", "DATETIME", "DatetimeOptimizer", "_DATETIME", "DictTokenizer", "wordinfo", "matchWord", "bw", "lastword", "filterWord", "chunk", "wordpos", "getPosInfo", "chunks", "getChunks", "assess", "sp", "has_D_V", "x", "a", "b", "prew", "nextw", "Math", "pow", "currchunk", "getTops", "nextcur", "top", "ass", "tops", "curri", "maxs", "ForeignTokenizer", "splitForeign", "lastcur", "lasttype", "SingleTokenizer", "splitSingle", "WildcardTokenizer", "lowertext", "stopword", "pangu", "panguExtend1", "panguExtend2", "names", "wildcard", "synonym", "dicts", "synonyms", "stopwords", "useDefault", "segmentInstance"], "mappings": ";;;;;;AAMA,IAAMA,OAAS,CACbC,IAAK,WACLC,IAAK,UACLC,IAAK,UACLC,IAAK,UACLC,IAAK,SACLC,IAAK,SACLC,IAAK,SACLC,IAAK,QACLC,IAAK,QACLC,KAAM,QACNC,IAAK,QACLC,IAAK,OACLC,IAAK,OACLC,IAAK,OACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,KACLC,IAAK,KACLC,IAAK,KACLC,IAAK,KACLC,IAAK,IACLC,IAAK,IACLC,KAAM,IACNC,KAAM,GACNC,KAAM,GACNC,KAAM,GACNC,KAAM,EACNC,KAAM,EACNC,IAAK,EACLC,IAAK,EACLC,IAAK,GAGDC,aAAe,CACnBhC,IAAK,UACLC,IAAK,WACLC,IAAK,SACLC,IAAK,SACLC,IAAK,SACLC,IAAK,WACLC,IAAK,KACLC,IAAK,KACLC,IAAK,SACLC,KAAM,MACNC,IAAK,SACLC,IAAK,MACLC,IAAK,KACLC,IAAK,SACLC,IAAK,SACLC,IAAK,MACLC,IAAK,MACLC,IAAK,SACLC,IAAK,SACLC,IAAK,OACLC,IAAK,OACLC,IAAK,WACLC,IAAK,MACLC,KAAM,KACNC,KAAM,KACNC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,KAAM,OACNC,IAAK,OACLC,IAAK,KACLC,IAAK,WAGDE,aAAe,CACnBjC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,KAAM,KACNC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,IAAK,IACLC,KAAM,KACNC,KAAM,KACNC,KAAM,KACNC,KAAM,KACNC,KAAM,KACNC,KAAM,IACNC,IAAK,IACLC,IAAK,KACLC,IAAK;AAGA,SAASG,oBAAoBC,EAAoBC,UAC/C,SAACC,MACFC,MAAMD,UACDD,EAASC,IAAiBD,EAASN;IAEtCS,EAAS;IACV,IAAMC,KAAOL,EACuB,GAAlCE,EAAeF,EAAWK,KAC7BD,EAAOE,KAAKL,EAASI;OAGrBD,EAAOG,OAAS,EACXN,EAASN,IAEXS,GAAAA,GAIX,IAAaI,SAAWT,oBAAoBnC,OAAQiC,cACvCY,SAAWV,oBAAoBnC,OAAQkC,cC/H/BY,gCAMPC,gCACLA,QAAUA,qDAUXC,EAAMC,MACNA,EAAQN,OAAS,QACbO,MAAM;IAGVV,EAAS,CAAC,CAAEW,EAAGH;OACnBC,EAAQG,QAAQ,SAAAC,GACdb,EAASa,EAAOC,MAAMd,KAEjBA,WC1BUe,gCAMPR,gCACLA,QAAUA,0DAUNS,EAAOP,OACZT,qBAAagB;OAEjBP,EAAQG,QAAQ,SAAAC,GACdb,EAASa,EAAOI,WAAWjB,KAEtBA,WCXUkB;mDAkBb,SAACC,MAEDC,MAAMC,QAAQF,GAChBA,EAAOP,QAAQU,EAAKC;QAGO,mBAAhBJ,EAAOK,KAChBL,EAAOK,KAAKF,GACZA,EAAKb,QAAQU,EAAOM,MAAMvB,KAAKiB;IAC1B,KACCN,EAAS,IAAIM,EAAOG;AAC1BA,EAAKb,QAAQI,EAAOY,MAAMvB,KAAKW,UAI5BS,oCAWE,SACTI,OACAD,4DAAO,QACPE;GAEIP,MAAMC,QAAQK,GAChBA,EAAKd,QAAQ,SAAAgB,UAAKN,EAAKO,SAASD;IAC3B,CAEAN,EAAKQ,KAAKL,QAAOH,EAAKQ,KAAKL,MAAQ,IACnCH,EAAKQ,eAAQL,aAAUH,EAAKQ,eAAQL,WAAW;IAC9CM,EAAQT,EAAKQ,KAAKL,MAClBO,EAASV,EAAKQ,eAAQL;AAE5BC,EACGZ,MAAM,SACNmB,IAAI,SAAAC,UACCP,EAAuBO,EAAKC,cACzBD,IAERtB,QAAQ,SAAAsB,OACDE,EAASF,EAAKpB,MAAM;GACN,EAAhBsB,EAAOjC,OAAY,KACfQ,EAAIyB,EAAO,GAAGC;AAKL,EAAX1B,EAAER,SACJ4B,EAAMpB,GAAK,CAAE2B,GAJEF,EAAO,GAING,GALDH,EAAO,IAMjBJ,EAAOrB,EAAER,UAAS6B,EAAOrB,EAAER,QAAU,IAC1C6B,EAAOrB,EAAER,QAAQQ,GAAKoB,EAAMpB,cAM/BW,mCASC,SAACG,aAAiBH,EAAKQ,KAAKL,+CAOpB,SAACC,MACbN,MAAMC,QAAQK,GAChBA,EAAKd,QAAQ,SAAAgB,UAAKN,EAAKkB,gBAAgBZ;IAClC,CAIAN,EAAKQ,KAAL,UAAiBR,EAAKQ,KAAL,QAAkB;IAClCC,EAAQT,EAAKQ,KAAL;AAEdJ,EACGZ,MAAM,SACNmB,IAAI,SAAAC,UAAQA,EAAKpB,MAAM,OACvBF,QAAQ,SAAAwB,MACa,EAAhBA,EAAOjC,OAAY,KACfsC,EAAKL,EAAO,GAAGC,OACfK,EAAKN,EAAO,GAAGC;AACrBN,EAAMU,GAAMC,EACRX,EAAMW,KAAQD,UACTV,EAAMW,aAMhBpB,4CAQU,SAACI,MACdN,MAAMC,QAAQK,GAChBA,EAAKd,QAAQ,SAAAgB,UAAKN,EAAKqB,iBAAiBf;IACnC,KACCH,KAAO;AAGRH,EAAKQ,KAAKL,QAAOH,EAAKQ,KAAKL,MAAQ;IAClCM,EAAQT,EAAKQ,KAAKL;AAExBC,EACGZ,MAAM,SACNmB,IAAI,SAAAC,UAAQA,EAAKG,SACjBzB,QAAQ,SAAAsB,GACHA,IACFH,EAAMG,IAAQ,YAKfZ,qCAcG,SAACd,EAAMoC,OACXC,EAAKvB;AACXsB,EAAUA,GAAW;IACjBE,EAAM;SA4BDC,EAAeC,OAClBC,EAAQ,EACNlB,EAAQc,EAAGK,QAAQ;OACzBF,EAAOA,EAAKf,IAAI,SAAAkB,UACVA,EAAKxC,KAAKoB,GACZkB,IACO,CAAEtC,EAAGoB,EAAMoB,EAAKxC,GAAI4B,EAAGY,EAAKZ,IAE9BY,IAEF,CAAEF,MAAAA,EAAOD,KAAAA,MAnClBxC,EACG4C,QAAQ,MAAO,MAEftC,MAAM,OACNF,QAAQ,SAAAyC,SACHA,EAAUA,EAAQhB,QACVlC,OAAS,QAGjBmD,EAAOT,EAAGU,UAAUzC,MAAMuC,EAASR,EAAGpC,QAAQ8C;AAOhC,GAJlBD,EAAOT,EAAGW,UAAUvC,WAAWqC,EAAMT,EAAGpC,QAAQ+C,YAIvCrD,SAAY2C,EAAMA,EAAIW,OAAOH,OAItCV,EAAQc,mBACVZ,EAAMA,EAAIa,OAAO,SAAAR,UAAQA,EAAKZ,IAAM/E,OAAOoB,OAgBzCgE,EAAQG,iBACP,KACG/C,EAAS+C,EAAeD;AAC5BA,EAAM9C,EAAOgD,WACS,EAAfhD,EAAOiD;GAIdL,EAAQgB,cAAe,KACnBC,EAAWhB,EAAGK,QAAQ;AAC5BJ,EAAMA,EAAIa,OAAO,SAAAR,WAAUA,EAAKxC,KAAKkD,YAInCjB,EAAQkB,SACVhB,EAAMA,EAAIb,IAAI,SAAAkB,UAAQA,EAAKxC,KAGtBmC,SA/NFtF,OAASA,YACTsE,KAAO,QACPrB,QAAU,CACb8C,UAAW,GACXC,UAAW,SAERD,UAAY,IAAIjD,UAAUyD,WAC1BP,UAAY,IAAIzC,UAAUgD,4DAiOxB/C,UACAA,EAAMiB,IAAI,SAAAkB,UAAQA,EAAKxC,IAAGqD,KAAK,kCAUlChD,EAAOiD,WACLnB,EAAM,GACRoB,EAAQ,EACRC,EAAI,EACF7B,EAAiB,iBAAN2B,EAAiB,IAAM,IAEjCE,EAAInD,EAAMb,QACXa,EAAMmD,GAAG7B,KAAO2B,GACdC,EAAQC,GAAGrB,EAAI5C,KAAKc,EAAMoD,MAAMF,EAAOC,IAC3CrB,EAAI5C,KAAKc,EAAMoD,MAAMD,EAAGA,EAAI,IAE5BD,IADAC,GAGAA;OAGAD,EAAQlD,EAAMb,OAAS,GACzB2C,EAAI5C,KAAKc,EAAMoD,MAAMF,EAAOlD,EAAMb,SAG7B2C,kCAWD9B,EAAOiD,EAAGI,GAChBA,EAAMtE,MAAMsE,GAAO,EAAIA;QACjB/B,EAAiB,iBAAN2B,EAAiB,IAAM,IAEjCI,EAAMrD,EAAMb,QAAQ,IACrBa,EAAMqD,GAAK/B,KAAO2B,EAAG,OAAOI;AAChCA,WAGM,WCzSClD,OAGX,WAAYZ,oEACLA,QAAUA,GAIND,YAAb;;;+IACS,kCADsBa,UAA/B,GAIaJ,YAAb;;;+IACS,kCADsBI,UAA/B,GCbamD,eAA6B,CACxC,CAAC,QAAS,UAAW,eACrB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,OAAQ,UAAW,aACpB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,QAAS,UAAW,YACrB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,UAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,UAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,WAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,WAClB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,QAAS,UAAW,aACrB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,YACpB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,OAAQ,UAAW,UACpB,CAAC,KAAM,UAAW,WAClB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,OAAQ,UAAW,WACpB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,UAClB,CAAC,MAAO,UAAW,UACnB,CAAC,MAAO,UAAW,UACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,SAAU,UAAW,eACtB,CAAC,OAAQ,UAAW,eACpB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,YACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,QAAS,UAAW,aACrB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,UAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,WAClB,CAAC,OAAQ,UAAW,WACpB,CAAC,OAAQ,UAAW,WACpB,CAAC,MAAO,UAAW,WACnB,CAAC,MAAO,UAAW,WACnB,CAAC,MAAO,UAAW,WACnB,CAAC,MAAO,UAAW,WACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,SAAU,UAAW,eACtB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,YACpB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,OAAQ,UAAW,YACpB,CAAC,KAAM,UAAW,SAClB,CAAC,KAAM,UAAW,WAClB,CAAC,MAAO,UAAW,SACnB,CAAC,KAAM,UAAW,SAClB,CAAC,KAAM,UAAW,SAClB,CAAC,KAAM,UAAW,YAClB,CAAC,QAAS,UAAW,eACrB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,cAClB,CAAC,OAAQ,UAAW,YACpB,CAAC,MAAO,UAAW,cACnB,CAAC,OAAQ,UAAW,cACpB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,YACnB,CAAC,OAAQ,UAAW,WACpB,CAAC,OAAQ,UAAW,WACpB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,WACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,QAAS,UAAW,eACrB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,cACpB,CAAC,MAAO,UAAW,cACnB,CAAC,QAAS,UAAW,cACrB,CAAC,OAAQ,UAAW,cACpB,CAAC,OAAQ,UAAW,cACpB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,QAAS,UAAW,aACrB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,cACpB,CAAC,OAAQ,UAAW,cACpB,CAAC,SAAU,UAAW,cACtB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,cACnB,CAAC,OAAQ,UAAW,cACpB,CAAC,OAAQ,UAAW,cACpB,CAAC,OAAQ,UAAW,YACpB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,cACnB,CAAC,OAAQ,UAAW,cACpB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,cAClB,CAAC,OAAQ,UAAW,cACpB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,cAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,WACnB,CAAC,OAAQ,UAAW,cACpB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,OAAQ,UAAW,aACpB,CAAC,QAAS,UAAW,aACrB,CAAC,KAAM,UAAW,aAClB,CAAC,QAAS,UAAW,aACrB,CAAC,MAAO,UAAW,aACnB,CAAC,OAAQ,UAAW,WACpB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,cACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,MAAO,UAAW,eACnB,CAAC,QAAS,UAAW,eACrB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,OAAQ,UAAW,eACpB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,QAAS,UAAW,eACrB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,KAAM,UAAW,SAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,aACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,IAAK,UAAW,aACjB,CAAC,IAAK,UAAW,aACjB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,eAClB,CAAC,IAAK,UAAW,YACjB,CAAC,IAAK,UAAW,aACjB,CAAC,IAAK,UAAW,eACjB,CAAC,IAAK,UAAW,cACjB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,gBAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,MAAO,UAAW,cACnB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,cAClB,CAAC,IAAK,UAAW,eACjB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,IAAK,UAAW,cACjB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,IAAK,UAAW,aACjB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,WAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,gBAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,IAAK,UAAW,eACjB,CAAC,IAAK,UAAW,eACjB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,aAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,YAClB,CAAC,KAAM,UAAW,YAClB,CAAC,IAAK,UAAW,YACjB,CAAC,IAAK,UAAW,cACjB,CAAC,IAAK,UAAW,eACjB,CAAC,KAAM,UAAW,aAClB,CAAC,IAAK,UAAW,YACjB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,cAClB,CAAC,KAAM,UAAW,eAClB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,YACnB,CAAC,OAAQ,UAAW,aACpB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,cACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,WACnB,CAAC,QAAS,UAAW,cACrB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,aACnB,CAAC,MAAO,UAAW,eACnB,CAAC,KAAM,UAAW,eAClB,CAAC,OAAQ,UAAW,eACpB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,eACnB,CAAC,MAAO,UAAW,YACnB,CAAC,MAAO,UAAW,aAGRC,QACX,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,+BACGD,eAAerC,IAAI,SAAAkB,UAAQA,EAAK,OCluBhBqB,uKAA2BzD,8DACnCC,WACDxD,EAAWuG,KAAKxD,QAAhB/C,OACJiH,EAAQ,EACLA,EAAQzD,EAAMb,QAAQ,KACrBuE,EAAO1D,EAAMyD,GACbE,EAAW3D,EAAMyD,EAAQ;AAC3BE,IAEEA,EAASpC,IAAM/E,EAAOkB,KAAO6F,OAAOK,SAASF,EAAK/D,KACpD+D,EAAKnC,EAAI/E,EAAOC,KAGdiH,EAAKnC,IAAM/E,EAAOW,KAAO4F,KAAKc,UAAUF,EAASpC,IAAMgC,OAAOK,SAASF,EAAK/D,KAC9E+D,EAAKnC,EAAI/E,EAAOC,MAIpBgH,GAAS,SAEJzD,oCAGC8D,MACJ1D,MAAMC,QAAQyD,UACTf,KAAKc,UAAUC,EAAI;IAEpBtH,EAAWuG,KAAKxD,QAAhB/C;OAENsH,IAAQtH,EAAOW,KACZ2G,IAAQtH,EAAO0B,MACf4F,IAAQtH,EAAO2B,MACf2F,IAAQtH,EAAO4B,MACf0F,IAAQtH,EAAOwB,MACf8F,IAAQtH,EAAOyB,MACf6F,IAAQtH,EAAOgC;ACpCxB,SAASuF,aAAaC,EAAiBC,OAC/BjF,EAAS;OACfgF,EAAMpE,QAAQ,SAAAsE,GACZlF,EAAOkF,GAAQD,IAEVjF,EAIF,IAAMmF,cAAgBJ,aAC3B,CAEE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IAEA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEF,GAGWK,cAAgBL,aAC3B,CACE,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MAEF,GAIWM,cAAgBN,aAC3B,CACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEF,GAIWO,cAAgBP,aAC3B,CACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEF,GAIWQ,YAAcR,aACzB,CACE,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEF,aAGa,CACbI,cAAAA,cACAC,cAAAA,cACAC,cAAAA,cACAC,cAAAA,cACAC,YAAAA,aC/7BmBC,qKAAyBlF,yDACtCU,WAGY0D,EAFVlH,EAASuG,KAAKxD,QAAQ/C,OACtBsF,EAAM,GACHqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,OAC1B,EAATO,EAAKnC,EACPO,EAAI5C,KAAKwE;SAILe,EAA+BD,EAAiBE,UAAUhB,EAAK/D;GACjE8E,EAAStF,OAAS,EACpB2C,EAAI5C,KAAKwE;aAKMiB,EADbC,EAAQ,EACHC,EAAK,EAASF,EAAMF,EAASI,GAAMA,IAC9BD,EAARD,EAAIG,GACNhD,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOH,EAAOD,EAAIG,EAAIF,KAE7C9C,EAAI5C,KAAK,CAAES,EAAGgF,EAAIhF,EAAG4B,EAAG/E,EAAOwB,OAC/B4G,EAAQD,EAAIG,EAAIH,EAAIhF,EAAER;IAElB6F,EAAQP,EAASA,EAAStF,OAAS;AACrC6F,EAAMF,EAAIE,EAAMrF,EAAER,OAASuE,EAAK/D,EAAER,QACpC2C,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOC,EAAMF,EAAIE,EAAMrF,EAAER,kBAG3C2C,sCAIQtC,EAAcyF,OACzBC,EAAgB;AACfnG,MAAMkG,KAAWC,EAAgBD;QAChCjG,EAAS,GACRkG,EAAgB1F,EAAKL,QAAQ,KAC9BgG,GAAO,EAELC,EAAK5F,EAAKuF,OAAOG,EAAe;GAClCE,KAAMhB,cAAe,KACjB3C,EAAKjC,EAAAA,EAAY0F,EAAgB,GACjCxD,EAAKlC,EAAAA,EAAY0F,EAAgB;AAEnCzD,KAAM4C,eAAiB3C,KAAM4C,cAC/Ba,EAAOC,EAAK3D,EAAKC,EACRD,KAAM8C,cACfY,EAAOC,EAAK3D,GAAMA,GAAOC,EAAKA,EAAK,SAIjC2D,EAAK7F,EAAAA,EAAY0F;IACV,IAATC,GAAkBE,KAAMlB,cAAe,KACnC1C,EAAKjC,EAAAA,EAAY0F,EAAgB,GACjCxD,EAAKlC,EAAAA,EAAY0F,EAAgB;AACnCzD,KAAM4C,eAAiB3C,KAAM4C,cAC/Ba,EAAOE,EAAK5D,EAAKC,EACRD,KAAM8C,cACfY,EAAOE,EAAK5D,GAAMA,GAAOC,EAAKA,EAAK,MAI1B,IAATyD,EACFD,KAEAlG,EAAOE,KAAK,CAAES,EAAGwF,EAAML,EAAGI,IAC1BA,GAAiBC,EAAKhG,eAGnBH,WC1EUsG,kKAAsBvF,8DAQ9BC,EAA4BuF,QAEX,IAAfA,IACTA,GAAa;QAGTxE,EAAQgC,KAAKxD,QAAQ2C,QAAQ,SAC7B1F,EAASuG,KAAKxD,QAAQ/C,OAExB2G,EAAI,EACJqC,EAAKxF,EAAMb,OAAS,EACjBgE,EAAIqC,GAAI,KACPC,EAAKzF,EAAMmD,GACXuC,EAAK1F,EAAMmD,EAAI,GAKfwC,EAAKF,EAAG9F,EAAI+F,EAAG/F;GACjB8F,EAAGlE,IAAMmE,EAAGnE,GAAKoE,KAAM5E,EACzBf,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAGgG,EACHpE,EAAGR,EAAM4E,GAAIpE,IAEfiE;QAKwB,GAArBC,EAAGlE,EAAI/E,EAAOC,MAAYiJ,EAAGnE,EAAI/E,EAAOkB,IAC3CsC,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAGgG,EACHpE,EAAG/E,EAAOC,MAEZ+I;QAMwB,GAArBC,EAAGlE,EAAI/E,EAAOS,KAAU,IAGD,GAArByI,EAAGnE,EAAI/E,EAAOS,MAAqB,MAATyI,EAAG/F,EAAW,CAC3CK,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG8F,EAAG9F,EAAI+F,EAAG/F,EACb4B,EAAG/E,EAAOS,MAEZuI;YAIwB,GAArBE,EAAGnE,EAAI/E,EAAOc,KAAU,CAC3B0C,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG8F,EAAG9F,EAAI+F,EAAG/F,EACb4B,EAAG/E,EAAOU,OAEZsI;aAKEK,EAAK7F,EAAMmD,EAAI,KACa,GAArB0C,EAAGtE,EAAI/E,EAAOS,OAAsB,MAATyI,EAAG/F,GAAsB,MAAT+F,EAAG/F,GAAsB,OAAT+F,EAAG/F,GAAa,CACpFK,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG8F,EAAG9F,EAAI+F,EAAG/F,EAAIkG,EAAGlG,EACpB4B,EAAG/E,EAAOS,MAEZuI,GAAM;aAMiB,GAAtBC,EAAGlE,EAAI/E,EAAOU,OAAiC,KAApBuI,EAAG9F,EAAEoF,QAAQ,IAAcW,EAAGnE,EAAI/E,EAAOS,aAEnE6I,EAAK,EACLC,EAAM,GACDC,EAAI7C,EAAI2C,EAAIE,EAAIR,EAAIQ,IAAK,KAC5BH;KACsB,IADtBA,EAAK7F,EAAMgG,IACPzE,EAAI/E,EAAOS;AACjB8I,GAAOF,EAAGlG,EACVmG,IAKJ9F,EAAM4F,OAAOzC,EAAG2C,EAAI,CAClBnG,EAAG8F,EAAG9F,EAAI+F,EAAG/F,EAAIoG,EACjBxE,EAAG/E,EAAOU,OAEZsI,GAAMM,EAAK,OAKb3C,YAIoB,IAAfoC,EAAsBvF,EAAQ+C,KAAK9C,WAAWD,GAAO,YC1G1DiG,WAAa,wFAAwFnG,MAAM,IAC3GoG,UAAY;AAClB,IAAK,IAAM/C,KAAK8C,WAAYC,UAAUD,WAAW9C,IAAM;IAElCgD,mKAAuBpG,8DAC/BC,WACHxD,EAASuG,KAAKxD,QAAQ/C,OAGxB2G,EAAI,EACJqC,EAAKxF,EAAMb,OAAS,EACpBiH,GAAa,EACbC,GAAS,EACNlD,EAAIqC,GAAI,KACT9B,EACA4C,MADA5C,EAAO1D,EAAMmD,IACM5B,IAAM/E,EAAO2B,MAASuF,EAAKnC,IAAM/E,EAAOS,KAAOyG,EAAK/D,EAAE4G,WAAW,GAAK;IAG1E,IAAfH,GAAwBE,EAC1BF,EAAajD,EACbA;SAIe,IAAXkD,GAA+B,MAAX3C,EAAK/D,MAMd,IAAX0G,GAAsC,KAAlBrG,EAAMmD,EAAI,GAAGxD,IAAyB,IAAb2G,GAAwB5C,EAAK/D,KAAKuG,WAahE,IAAfE,IAAyBE,GAAY5C,EAAK/D,KAAKuG,aAQrDG,EADAD,GAAa,GANTjD;SAbIqD,EAASxG,EAAMoD,MAAMgD,EAAYjD;AACrCnD,EAAM4F,OAAOQ,EAAYI,EAAOrH,OAAQ,CACtCQ,EAAGwG,EAAeM,eAAeD,GACjCjF,EAAG/E,EAAOgC,MAEZ2E,EAAIiD,EAAa,EACjBZ,GAAMgB,EAAOrH,OAAS,EAEtBkH,EADAD,GAAa,OAbbC,GAAS,EACTlD,OA8BFiD,GAAcC,GAAUrG,EAAMwF,KAE5Bc,MADA5C,EAAO1D,EAAMwF,IACMjE,IAAM/E,EAAO2B,MAASuF,EAAKnC,IAAM/E,EAAOS,KAAOyG,EAAK/D,KAAKuG,YAClE,CACRM,EAASxG,EAAMoD,MAAMgD,EAAYpG,EAAMb;AAC3Ca,EAAM4F,OAAOQ,EAAYI,EAAOrH,OAAQ,CACtCQ,EAAGwG,EAAeM,eAAeD,GACjCjF,EAAG/E,EAAOgC,aAKTwB,2CASaA,WAEJ0D,EADZ5B,EAAM9B,EAAM,GAAGL,EACVwD,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,IACvCrB,GAAO4B,EAAK/D;OAEPmC,WCnFP4E,UAAY;AAOhBA,UAAYA,UAAU5G,MAAM;AAC5B,IAAM+C,SAAW,GACX8D,UAAY;AAClB,IAAK,IAAMxD,OAAKuD,aACO,KAAjBA,UAAUvD,UACRyD,IAAMF,UAAUvD,KAAGhE;AAEpBwH,UADL9D,SAAS6D,UAAUvD,MAAMyD,OACJD,UAAUC,KAAO,IACtCD,UAAUC,KAAKF,UAAUvD,MAAMyD,QAGZC,yKAA6BvH,yDAC1CU,WAGY0D,EAFVlH,EAASuG,KAAKxD,QAAQ/C,OACtBsF,EAAM,GACHqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,OAC1B,EAATO,EAAKnC,EACPO,EAAI5C,KAAKwE;SAILoD,EAAWD,EAAqBE,cAAcrD,EAAK/D;GACrDmH,EAAS3H,OAAS,EACpB2C,EAAI5C,KAAKwE;aAKMsD,EADbpC,EAAQ,EACHC,EAAK,EAAQmC,EAAKF,EAASjC,GAAMA,IAC7BD,EAAPoC,EAAGlC,GACLhD,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOH,EAAOoC,EAAGlC,EAAIF,KAGhC,KAARoC,EAAGrH,GACLmC,EAAI5C,KAAK,CAAES,EAAGqH,EAAGrH,EAAG4B,EAAG/E,EAAOoB,MAEhCgH,EAAQoC,EAAGlC,EAAIkC,EAAGrH,EAAER;IAEhB8H,EAASH,EAASA,EAAS3H,OAAS;AACtC8H,EAAOnC,EAAImC,EAAOtH,EAAER,OAASuE,EAAK/D,EAAER,QACtC2C,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOkC,EAAOnC,EAAImC,EAAOtH,EAAER,kBAG7C2C,0CAUYtC,EAAc6D,GAC7BtE,MAAMsE,KAAMA,EAAM;QAChBvB,EAAM,GACRoF,GAAU,EACP7D,EAAM7D,EAAKL,QAAQ,KACnB,IAAMgE,KAAKwD,UAAW,KACrBhH,EAAIH,EAAKuF,OAAO1B,EAAKF;GACrBxD,KAAKgH,UAAUxD,GAAI,CACrBrB,EAAI5C,KAAK,CAAES,EAAAA,EAAGmF,EAAGzB,IACjB6D,GAAU;OAId7D,IAAmB,IAAZ6D,EAAoB,EAAIvH,EAAER,OACjC+H,GAAU,SAGLpF,WC7ELqF,SAAW,CAAC,UAAW,WAAY,SAAU,UAAW,aAE1DC,iBAAmB;AACvB,IAAK,IAAIjE,OAAKgE,SACRA,SAAShE,KAAGhE,OAASiI,mBACvBA,iBAAmBD,SAAShE,KAAGhE;AAInC,IAAMkI,SAAW,CACf,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACA,IACA,IACA,IACA,IACA,IACA,KAEIC,QAAU;AAChB,IAAK,IAAInE,OAAKkE,SACZC,QAAQD,SAASlE,MAAM;IAGJoE,iKAAqBjI,yDAClCU,WAGY0D,EAFVlH,EAASuG,KAAKxD,QAAQ/C,OACtBsF,EAAM,GACHqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,OAC1B,EAATO,EAAKnC,EACPO,EAAI5C,KAAKwE;SAIL8D,EAAUD,EAAaE,SAAS/D,EAAK/D;GACvC6H,EAAQrI,OAAS,EACnB2C,EAAI5C,KAAKwE;aAKMiB,EADbC,EAAQ,EACHC,EAAK,EAASF,EAAM6C,EAAQ3C,GAAMA,IAC7BD,EAARD,EAAIG,GACNhD,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOH,EAAOD,EAAIG,EAAIF,KAE7C9C,EAAI5C,KAAK,CAAES,EAAGgF,EAAIhF,EAAG4B,EAAG/E,EAAOgC,MAC/BoG,EAAQD,EAAIG,EAAIH,EAAIhF,EAAER;IAElBuI,EAAUF,EAAQA,EAAQrI,OAAS;AACrCuI,EAAQ5C,EAAI4C,EAAQ/H,EAAER,OAASuE,EAAK/D,EAAER,QACxC2C,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAO2C,EAAQ5C,EAAI4C,EAAQ/H,EAAER,kBAI/C2C,qCAUOtC,EAAc6D,GACxBtE,MAAMsE,KAAMA,EAAM;QAChBvB,EAAM,GACRmB,GAAI,EACDI,EAAM7D,EAAKL,QAAQ,KAEd,IAAN8D,GAAeI,EAAM7D,EAAKL,OAASiI,sBAChC,IAAWO,EAAPxE,EAAI,EAAUwE,EAAOR,SAAShE,GAAKA,OACtC3D,EAAKuF,OAAO1B,EAAKsE,EAAKxI,UAAYwI,EAAM,CAC1C1E,EAAII,EACJA,GAAOsE,EAAKxI,OAAS;YAIV,IAAN8D,GAAiBzD,EAAAA,EAAY6D,KAAQiE,UAE9CxF,EAAI5C,KAAK,CACPS,EAAGH,EAAKuF,OAAO9B,EAAGI,EAAMJ,GACxB6B,EAAG7B,IAELA,GAAI;AAENI,WAGQ,IAANJ,GACFnB,EAAI5C,KAAK,CACPS,EAAGH,EAAKuF,OAAO9B,EAAGI,EAAMJ,GACxB6B,EAAG7B,IAIAnB,WC9KU8F,qKAAyB7H,8DACjCC,WACHxD,EAASuG,KAAKxD,QAAQ/C,OACxB2G,EAAI,EAGDA,EAAInD,EAAMb,QAAQ,KACjBuE,EAAO1D,EAAMmD,GACbQ,EAAW3D,EAAMmD,EAAI;GACvBQ,EAAU,IAIVA,IACe,MAAXD,EAAK/D,GAAwB,MAAX+D,EAAK/D,KACvBgE,EAAShE,KAAKwE,eAAiBR,EAAShE,KAAKyE,eACjD,CACApE,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG+D,EAAK/D,EAAIgE,EAAShE,EACrB4B,EAAG/E,EAAOwB,OAEZmF;aAMCO,EAAK/D,KAAKwE,eAAiBT,EAAK/D,KAAKyE,gBACL,GAA5BT,EAASpC,EAAI/E,EAAOwB,OAAa2F,EAAShE,EAAER,QAAU,EAC3D,CACAa,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG+D,EAAK/D,EAAIgE,EAAShE,EACrB4B,EAAG/E,EAAOwB,OAEZmF;cAKGO,EAAKnC,IAAMoC,EAASpC,KAEpBmC,EAAK/D,KAAK4E,aAAeb,EAAK/D,IAAMgE,EAAShE,GAC1C+D,EAAK/D,KAAK0E,eAAiBV,EAAShE,KAAK2E,eAC7C,CACAtE,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG+D,EAAK/D,EAAIgE,EAAShE,EACrB4B,EAAG/E,EAAOwB;IAGN6J,EAAU7H,EAAMmD,EAAI;AACtB0E,IAAYA,EAAQlI,KAAKwE,eAAiB0D,EAAQlI,KAAKyE,eACzDpE,EAAM4F,OAAOzC,EAAI,EAAG,EAAG,CACrBxD,EAAGkI,EAAQlI,EAAI+D,EAAK/D,EAAIgE,EAAShE,EACjC4B,EAAG/E,EAAOwB,OAGZmF;WAODO,EAAK/D,KAAKwE,eAAiBT,EAAK/D,KAAKyE,gBAAoBV,EAAKnC,GAAMoC,EAASpC,GAEhFvB,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG+D,EAAK/D,EAAIgE,EAAShE,EACrB4B,EAAG/E,EAAOwB,OAMhBmF,QAIFA,EAAI,EACGA,EAAInD,EAAMb,QAAQ,KACjBuE,EAAO1D,EAAMmD,GACbQ,EAAW3D,EAAMmD,EAAI;AACvBQ,IAEGD,EAAK/D,KAAKwE,eAAiBT,EAAK/D,KAAKyE,gBAAkBT,EAAShE,KAAK4E,aACxEvE,EAAM4F,OAAOzC,EAAG,EAAG,CACjBxD,EAAG+D,EAAK/D,EAAIgE,EAAShE,EACrB4B,EAAG/E,EAAOwB,OAEZmF,KAMJA,WAGKnD,WCpGL8H,eAAiB,CAAC,KAAM,IAAK,KAAM,KAAM,IAAK,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,KAAM,IAAK,KAAM,IAAK,MACpGC,SAAW;AAEjB,IAAK,IAAM5E,OAAK2E,eACdC,SAASD,eAAe3E,MAAM2E,eAAe3E,KAAGhE;IAG7B6I,sKAA0BjI,8DAQlCC,GAKK+C,KAAKxD,QAAQ2C,QAAQ;QAC7B1F,EAASuG,KAAKxD,QAAQ/C,OAExB2G,EAAI,EACJqC,EAAKxF,EAAMb,OAAS,EACjBgE,EAAIqC,GAAI,KAETE,EAAK1F,EAAMmD,EAAI;GAGO,IAJtBsC,EAAKzF,EAAMmD,IAIP5B,EAAI/E,EAAOS,MAGbyI,EAAG/F,KAAKoI,kBACNpC,EAAKF,EAAG9F,EAAI+F,EAAG/F,EACfiH,EAAM,IAEG,KACPnB;AACAC,EAAK1F,EAAMmD,EAAIyD,EAAM;MADrBnB,EAAKzF,EAAMmD,EAAIyD,KAETlB,GAA4B,GAArBD,EAAGlE,EAAI/E,EAAOS,MAAYyI,EAAG/F,KAAKoI;AACjDnB,GAAO,EACPjB,GAAMF,EAAG9F,EAAI+F,EAAG/F,EAKpBK,EAAM4F,OAAOzC,EAAGyD,EAAK,CACnBjH,EAAGgG,EACHpE,EAAG/E,EAAOiB,MAEZ+H,GAAMoB,EAAM,OAOhBzD,WAGKnD,WC5DLiI,UAAY,CAAC,KAAM,IAAK,KAAM,KAAM,IAAK,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,KAAM,IAAK,KAAM,IAAK,MAC/FF,WAAW;AACjB,IAAK,IAAM5E,OAAK8E,UAAWF,WAASE,UAAU9E,MAAM8E,UAAU9E,KAAGhE;IAQ5C+I,kKAAsB5I,yDACnCU,WAKY0D,EAFV3C,EAAQgC,KAAKxD,QAAQ2C,QAAQ,SAC7BJ,EAAM,GACHqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,OAC1B,EAATO,EAAKnC,EACPO,EAAI5C,KAAKwE;SAILyE,EAAWpF,KAAKqF,UAAU1E,EAAK/D,EAAG,EAAGK,EAAMmD,EAAI;GACjDgF,EAAShJ,OAAS,EACpB2C,EAAI5C,KAAKwE;aAKM2E,EADbzD,EAAQ,EACHC,EAAK,EAAQwD,EAAKF,EAAStD,GAAMA,IAC7BD,EAAPyD,EAAGvD,GACLhD,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOH,EAAOyD,EAAGvD,EAAIF,KAE5C9C,EAAI5C,KAAK,CAAES,EAAG0I,EAAG1I,EAAG4B,EAAGR,EAAMsH,EAAG1I,GAAG4B,IACnCqD,EAAQyD,EAAGvD,EAAIuD,EAAG1I,EAAER;IAEhBmJ,EAAWH,EAASA,EAAShJ,OAAS;AACxCmJ,EAASxD,EAAIwD,EAAS3I,EAAER,OAASuE,EAAK/D,EAAER,QAC1C2C,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOuD,EAASxD,EAAIwD,EAAS3I,EAAER,kBAGjD2C,oCAWCtC,EAAM6D,EAAKwE,GACf9I,MAAMsE,KAAMA,EAAM;QAChBvB,EAAM,GAENf,EAAQgC,KAAKxD,QAAQ2C,QAAQ,UAE5BmB,EAAM7D,EAAKL,QAAQ,KACnB,IAAMgE,KAAKpC,EAAO,KACfpB,EAAIH,EAAKuF,OAAO1B,EAAKF;AACvBxD,KAAKoB,EAAMoC,IACbrB,EAAI5C,KAAK,CAAES,EAAAA,EAAGmF,EAAGzB,EAAK/B,EAAGP,EAAMoC,GAAGxD,GAAG2B,IAGzC+B,WAGKN,KAAKwF,WAAWzG,EAAK+F,EAASrI,sCAY5BQ,EAAO6H,EAASrI,WAsBTgJ,EArBVhM,EAASuG,KAAKxD,QAAQ/C,OACtBuE,EAAQgC,KAAKxD,QAAQ2C,QAAQ,SAI7BuG,EAAUP,EAAcQ,WAAW1I,EAAOR,GAW1CmJ,EAAST,EAAcU,UAAUH,EAAS,EAAGjJ,GAE7CqJ,EAAS,GAGN1F,EAAI,EAAWqF,EAAQG,EAAOxF,GAAKA,IAAK,KASzC2F,EAAKtJ,EAAKL,OAASqJ,EAAMrJ,OAE3B4J,IAVJF,EAAO1F,GAAK,CACV6F,EAAGR,EAAMrJ,OACT8J,EAAG,EACHC,EAAG,EACHpE,EAAG,EACHlE,EAAG;GAQDiH,MACEsB,EAAO,CAAExJ,EAAGkI,EAAQlI,EAAG4B,EAAGsG,EAAQtG,EAAGD,EAAGuG,EAAQvG;KAEpD6H,GAAO;IAEJ,IAAWxJ,EAAPqG,EAAI,EAAOrG,EAAI6I,EAAMxC,GAAKA,IAAK,IAClCrG,EAAEA,KAAKoB,MACTpB,EAAE4B,EAAIR,EAAMpB,EAAEA,GAAG4B,EACjBsH,EAAO1F,GAAG8F,GAAKtJ,EAAE2B,EAGb6H,EAAM,CAEoB,GAAvBA,EAAK5H,EAAI/E,EAAOS,OAA2C,GAA7B8D,EAAMpB,EAAEA,GAAG4B,EAAI/E,EAAOc,MAAYqC,EAAEA,KAAKoI,aAC1Ec,EAAO1F,GAAGvC,IAGa,GAApBjB,EAAE4B,EAAI/E,EAAOmB,OAChBoL,GAAU,EAKkB,GAAvBI,EAAK5H,EAAI/E,EAAOC,MACnBoM,EAAO1F,GAAGvC,MAKc,GAAxBuI,EAAK5H,EAAI/E,EAAOyB,OAAakL,EAAK5H,EAAI/E,EAAO0B,MAAgC,GAAvBiL,EAAK5H,EAAI/E,EAAOC,QAC/C,GAApBkD,EAAE4B,EAAI/E,EAAOW,MACS,GAArBwC,EAAE4B,EAAI/E,EAAOwB,OACQ,GAArB2B,EAAE4B,EAAI/E,EAAOyB,OACQ,GAArB0B,EAAE4B,EAAI/E,EAAO4B,OACQ,GAArBuB,EAAE4B,EAAI/E,EAAO0B,QAEnB2K,EAAO1F,GAAGvC,IAGgB,GAAvBuI,EAAK5H,EAAI/E,EAAOM,OAAa6C,EAAE4B,EAAkB,EAAb/E,EAAOS,KAAY0C,EAAE4B,EAAmB,EAAd/E,EAAOU,OAExE2L,EAAO1F,GAAGvC,KAITuI,EAAKxJ,KAAKwE,eAAiBgF,EAAKxJ,KAAKyE,iBACb,GAApBzE,EAAE4B,EAAI/E,EAAOW,MAAkC,GAArBwC,EAAE4B,EAAI/E,EAAO4B,QAG5CyK,EAAO1F,GAAGvC;IAINwI,EAAQZ,EAAMxC,EAAI;AACpBoD,IACEA,EAAMzJ,KAAKoB,IACbqI,EAAM7H,EAAIR,EAAMqI,EAAMzJ,GAAG4B,GAGF,GAApB5B,EAAE4B,EAAI/E,EAAOG,MAAYwM,EAAK5H,IAAM6H,EAAM7H,GAC7CsH,EAAO1F,GAAGvC,IAID,MAARjB,EAAEA,GAAqB,MAARA,EAAEA,KACW,GAAxByJ,EAAM7H,EAAI/E,EAAOW,MACS,GAAzBiM,EAAM7H,EAAI/E,EAAOwB,OACQ,GAAzBoL,EAAM7H,EAAI/E,EAAOyB,OACQ,GAAzBmL,EAAM7H,EAAI/E,EAAO4B,OACQ,GAAzBgL,EAAM7H,EAAI/E,EAAO0B,SAEvB2K,EAAO1F,GAAGvC,GAAK,YAMrBiI,EAAO1F,GAAG2B;AAGZ+D,EAAO1F,GAAG+F,GAAKG,KAAKC,IAAIR,EAAKnJ,EAAEA,EAAER,OAAQ,GACzCgK,EAAOX,EAAMxC,IAGC,IAAZ+C,IAAmBF,EAAO1F,GAAGvC,GAAK,IAEtCiI,EAAO1F,GAAG8F,EAAIJ,EAAO1F,GAAG8F,EAAIT,EAAMrJ,OAClC0J,EAAO1F,GAAG+F,EAAIL,EAAO1F,GAAG+F,EAAIV,EAAMrJ,WAQpBuE,EAHV6F,EAAYZ,EADNT,EAAcsB,QAAQX;IAIzB1F,EAAI,EAAUO,EAAO6F,EAAUpG,GAAKA,IACrCO,EAAK/D,KAAKoB,GACdwI,EAAU3D,OAAOzC,IAAK;OAGpBoG,uCAYUvJ,EAAOR,WAGPkE,EAFV+E,EAAU,GAEPtF,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,IAClCsF,EAAQ/E,EAAKoB,KAChB2D,EAAQ/E,EAAKoB,GAAK,IAEpB2D,EAAQ/E,EAAKoB,GAAG5F,KAAKwE;IAGdP,EAAI,EAAGA,EAAI3D,EAAKL,OAAQgE,IAC1BsF,EAAQtF,KACXsF,EAAQtF,GAAK,CAAC,CAAExD,EAAGH,EAAAA,EAAY2D,GAAI2B,EAAG3B,EAAG7B,EAAG;OAIzCmH,oCAWQA,EAAS3E,WAClB9D,EAAQyI,EAAQ3E,IAAQ,GACxBhC,EAAM,GACHqB,EAAI,EAAGA,EAAInD,EAAMb,OAAQgE,IAAK,KAC/BO,EAAO1D,EAAMmD,GAEbsG,EAAU/F,EAAKoB,EAAIpB,EAAK/D,EAAER;GAC3BsJ,EAAQgB,WAGLd,EAAST,EAAcU,UAAUH,EAASgB,GACvCzD,EAAI,EAAGA,EAAI2C,EAAOxJ,OAAQ6G,IACjClE,EAAI5C,KAAK,CAACwE,GAAMjB,OAAOkG,EAAO3C;KAJhClE,EAAI5C,KAAK,CAACwE,WAQP5B,kCASM+G,WAEPa,EAAM,CACVV,EAAGH,EAAO,GAAGG,EACbC,EAAGJ,EAAO,GAAGI,EACbC,EAAGL,EAAO,GAAGK,EACbpE,EAAG+D,EAAO,GAAG/D,EACblE,EAAGiI,EAAO,GAAGjI,GAENuC,EAAI,EAASwG,EAAMd,EAAO1F,GAAKA,IAC1BuG,EAAIT,EAAZU,EAAIV,IAAWS,EAAIT,EAAIU,EAAIV,GAC3BU,EAAIT,EAAIQ,EAAIR,IAAGQ,EAAIR,EAAIS,EAAIT,GACnBQ,EAAI5E,EAAZ6E,EAAI7E,IAAW4E,EAAI5E,EAAI6E,EAAI7E,GAC3B6E,EAAI/I,EAAI8I,EAAI9I,IAAG8I,EAAI9I,EAAI+I,EAAI/I,GACnB8I,EAAIV,EAAZW,EAAIX,IAAWU,EAAIV,EAAIW,EAAIX;IAMjBW,EADVC,EAAO;IACJzG,EAAI,EAASwG,EAAMd,EAAO1F,GAAKA,IACtCyG,EAAKzG,GAAK,EAEVyG,EAAKzG,IAAwB,KAAjBuG,EAAIV,EAAIW,EAAIX,GAEXU,EAAIT,GAAbU,EAAIV,IAAYW,EAAKzG,IAAM,GAE3BwG,EAAIT,GAAKQ,EAAIR,IAAGU,EAAKzG,IAAM,GAE/ByG,EAAKzG,IAAMuG,EAAI5E,EAAI6E,EAAI7E,EAEvB8E,EAAKzG,IAAoD,GAA7CwG,EAAI/I,EAAI,EAAI8I,EAAI9I,EAAI+I,EAAI/I,EAAI+I,EAAI/I,EAAI8I,EAAI9I;IAMlDiJ,EAAQ,EACRC,EAAOF,EAAK;IACX,IAAIzG,KAAKyG,EAAM,KACZ3G,EAAI2G,EAAKzG;GACP2G,EAAJ7G,EACF4G,EAAQ1G,EACR2G,EAAO7G;KACF,GAAIA,IAAM6G,EAAM,KAEjBb,EAAI,EACJC,EAAI;AACJL,EAAO1F,GAAG2B,EAAI+D,EAAOgB,GAAO/E,EAAGmE,IAC9BC,IACaL,EAAOgB,GAAOZ,EAA5BJ,EAAO1F,GAAG8F,EAAqBA,IAC9BC,IACDL,EAAO1F,GAAG6F,EAAIH,EAAOgB,GAAOb,EAAGC,IAC9BC,IACGA,EAAJD,IACFY,EAAQ1G,EACR2G,EAAO7G,WAIN4G,WCtVUE,qKAAyBzK,yDACtCU,WAGY0D,EADZ5B,EAAM,GACDqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,IACnCO,EAAKnC,EACPO,EAAI5C,KAAKwE,GAGT5B,EAAMA,EAAIW,OAAOM,KAAKiH,aAAatG,EAAK/D;OAGrCmC,uCAUItC,OACLhD,EAASuG,KAAKxD,QAAQ/C,OAEtBsF,EAAM,GAGRmI,EAAU,EACVC,EAAW;AAGN,QAFLpF,EAAItF,EAAK+G,WAAW,KAENzB,GAAK,QAAOA,GAAK,OAEXoF,EAAf,IAALpF,GAAWA,GAAK,GAAetI,EAAOS,IAC3B,IAAL6H,GAAWA,GAAK,IAAa,IAALA,GAAWA,GAAK,IAErCtI,EAAO2B,KACF3B,EAAO+B;IAEpB,IAAI4E,EAAI,EAAGA,EAAI3D,EAAKL,OAAQgE,IAAK,KAChC2B;GAEK,QAFLA,EAAItF,EAAK+G,WAAWpD,KAEN2B,GAAK,QAAOA,GAAK,OAE1B,IAALA,GAAWA,GAAK,GAAI,IAClBoF,IAAa1N,EAAOS,IAAK,KACvB0I,EAAK,CAAEhG,EAAGH,EAAKuF,OAAOkF,EAAS9G,EAAI8G;AACnCC,IAAa1N,EAAO+B,MAAKoH,EAAGpE,EAAI2I,GACpCpI,EAAI5C,KAAKyG,GACTsE,EAAU9G,EAEZ+G,EAAW1N,EAAOS,SACb,GAAU,IAAL6H,GAAWA,GAAK,IAAa,IAALA,GAAWA,GAAK,IAAM,IAEpDoF,IAAa1N,EAAO2B,KAAM,CACxBwH,EAAK,CAAEhG,EAAGH,EAAKuF,OAAOkF,EAAS9G,EAAI8G;AACnCC,IAAa1N,EAAO+B,MAAKoH,EAAGpE,EAAI2I,GACpCpI,EAAI5C,KAAKyG,GACTsE,EAAU9G,EAEZ+G,EAAW1N,EAAO2B,UAGd+L,IAAa1N,EAAO+B,MACtBuD,EAAI5C,KAAK,CACPS,EAAGH,EAAKuF,OAAOkF,EAAS9G,EAAI8G,GAC5B1I,EAAG,CAAC2I,KAEND,EAAU9G,GAEZ+G,EAAW1N,EAAO+B,IAIlBoH,EAAK,CAAEhG,EAAGH,EAAKuF,OAAOkF,EAAS9G,EAAI8G;OACnCC,IAAa1N,EAAO+B,MAAKoH,EAAGpE,EAAI2I,GACpCpI,EAAI5C,KAAKyG,GAGF7D,WC/EUqI,oKAAwB7K,yDACrCU,WAGY0D,EADZ5B,EAAM,GACDqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,IACnCO,EAAKnC,EACPO,EAAI5C,KAAKwE,GAGT5B,EAAMA,EAAIW,OAAOM,KAAKqH,YAAY1G,EAAK/D;OAGpCmC,sCAUGtC,EAAc6D,OAClB7G,EAASuG,KAAKxD,QAAQ/C;AACxBuC,MAAMsE,KAAMA,EAAM;QAChBvB,EAAM,GACLuB,EAAM7D,EAAKL,QAChB2C,EAAI5C,KAAK,CACPS,EAAGH,EAAAA,EAAY6D,GACf9B,EAAG/E,EAAO+B,MAEZ8E;OAEKvB,WCjCUuI,sKAA0B/K,yDACvCU,WAIY0D,EAFV3C,EAAQgC,KAAKxD,QAAQ2C,QAAQ,YAC7BJ,EAAM,GACHqB,EAAI,EAAUO,EAAO1D,EAAMmD,GAAKA,OAC1B,EAATO,EAAKnC,EACPO,EAAI5C,KAAKwE;SAILyE,EAAWpF,KAAKqF,UAAU1E,EAAK/D;GACjCwI,EAAShJ,OAAS,EACpB2C,EAAI5C,KAAKwE;aAKM2E,EADbzD,EAAQ,EACHC,EAAK,EAAQwD,EAAKF,EAAStD,GAAMA,IAC7BD,EAAPyD,EAAGvD,GACLhD,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOH,EAAOyD,EAAGvD,EAAIF,KAE5C9C,EAAI5C,KAAK,CAAES,EAAG0I,EAAG1I,EAAG4B,EAAGR,EAAMsH,EAAG1I,EAAEwB,eAAeI,IACjDqD,EAAQyD,EAAGvD,EAAIuD,EAAG1I,EAAER;IAEhBmJ,EAAWH,EAASA,EAAShJ,OAAS;AACxCmJ,EAASxD,EAAIwD,EAAS3I,EAAER,OAASuE,EAAK/D,EAAER,QAC1C2C,EAAI5C,KAAK,CAAES,EAAG+D,EAAK/D,EAAEoF,OAAOuD,EAASxD,EAAIwD,EAAS3I,EAAER,kBAGjD2C,oCAUCtC,EAAc6D,GAClBtE,MAAMsE,KAAMA,EAAM;QAChBvB,EAAM,GAENf,EAAQgC,KAAKxD,QAAQ2C,QAAQ,aAE7BoI,EAAY9K,EAAK2B,cAChBkC,EAAM7D,EAAKL,QAAQ,KACpBoL,GAAW;IACV,IAAMpH,KAAKpC,EACVuJ,EAAUvF,OAAO1B,EAAKF,KAAMpC,EAAMoC,KACpCoH,EAAW,CAAE5K,EAAGH,EAAKuF,OAAO1B,EAAKF,GAAI2B,EAAGzB;CAG3B,IAAbkH,GACFzI,EAAI5C,KAAKqL,GACTlH,GAAOkH,EAAS5K,EAAER,QAElBkE,WAGGvB,WCnCErC,QAAU,CAErB8H,aACA8C,kBACAxD,qBACAkD,iBAEA7B,cACA1D,iBAEA2B,eACAyB,iBACAtC,cACA0C,kBACAxE,oBCnCWgH,kirhFAEAC,guKACAC,w/yBAEAC,671EAEAC,qeAEAC,m8hCAEAN,ijEAGAO,MAAkB,CAACN,MAAOC,aAAcC,aAAcC,MAAOC,UAC7DG,SAAqB,CAACF,SACtBG,UAAsB,CAACT;ACa7B,SAASU,WAAWC,UACzBA,EAAgB3K,IAAId,SACpByL,EAAgBrK,SAASiK,OACzBI,EAAgB1J,gBAAgBuJ,UAChCG,EAAgBvJ,iBAAiBqJ,WAC1BE"}