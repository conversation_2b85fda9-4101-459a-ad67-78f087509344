/**
 * 银行联行号查询系统 - 数据导入脚本
 *
 * 该脚本用于将 CSV 格式的银行联行号数据导入到 MongoDB 数据库中。
 * 数据源文件：structured_bank_data_with_area.csv
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { MongoClient } = require('mongodb');
const { pinyin } = require('pinyin-pro');

// 配置信息
const config = {
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
  dbName: 'bank_code_db',
  csvFile: path.resolve(__dirname, '../data/structured_bank_data_with_area.csv'),
  batchSize: 1000,  // 批量导入的记录数
  logInterval: 5000  // 日志记录间隔
};

// 生成拼音
function generatePinyin(text) {
  if (!text) return '';

  try {
    return pinyin(text, { toneType: 'none', type: 'array' }).join('');
  } catch (error) {
    console.error(`Error generating pinyin for "${text}":`, error);
    return text; // 如果拼音生成失败，返回原文本
  }
}

// 生成拼音首字母
function generatePinyinInitials(text) {
  if (!text) return '';

  try {
    return pinyin(text, { pattern: 'first', toneType: 'none', type: 'array' }).join('');
  } catch (error) {
    console.error(`Error generating pinyin initials for "${text}":`, error);
    return text.charAt(0); // 如果拼音生成失败，返回第一个字符
  }
}

// 提取地址关键词
function extractAddressKeywords(address) {
  if (!address) return [];

  // 简单的关键词提取逻辑，实际应用中可能需要更复杂的算法
  const keywords = [];

  // 提取省市区
  const regionPattern = /(北京市|上海市|天津市|重庆市|[^市]+?省|.+?市|.+?区|.+?县)/g;
  const regions = address.match(regionPattern) || [];
  keywords.push(...regions);

  // 提取街道、路名等
  const streetPattern = /([^区县市省]+?路|.+?街|.+?道|.+?大厦|.+?广场)/g;
  const streets = address.match(streetPattern) || [];
  keywords.push(...streets);

  // 去重
  return [...new Set(keywords)];
}

// 生成搜索文本
function generateSearchText(record) {
  const parts = [
    record.bank_short_name,
    record.bank_name,
    record.branch_name_clean,
    record.province,
    record.city,
    record.district,
    record.address
  ];

  return parts.filter(Boolean).join(' ');
}

// 根据银行代码获取银行类型
function getBankType(bankCode) {
  const code = bankCode.substring(0, 3);

  if (['102', '103', '104', '105'].includes(code)) {
    return '国有银行';
  } else if (['301', '302', '303', '304', '305', '306', '307', '308', '309', '310'].includes(code)) {
    return '股份制银行';
  } else if (code.startsWith('313')) {
    return '城市商业银行';
  } else if (code.startsWith('314')) {
    return '农村商业银行';
  } else if (code.startsWith('402')) {
    return '农村信用社';
  } else if (code === '403') {
    return '邮政储蓄银行';
  } else {
    return '其他银行';
  }
}

// 创建索引
async function createIndexes(db) {
  console.log('Creating indexes...');

  try {
    // 创建 banks 集合索引
    await db.collection('banks').createIndex({ "bank_code": 1 }, { unique: true });
    await db.collection('banks').createIndex({ "bank_name": 1 });
    await db.collection('banks').createIndex({ "bank_short_name": 1 });
    await db.collection('banks').createIndex({ "bank_name_pinyin": 1 });
    await db.collection('banks').createIndex({ "bank_type": 1 });
    await db.collection('banks').createIndex(
      { "bank_name": "text", "bank_short_name": "text", "bank_name_pinyin": "text" },
      { weights: { "bank_short_name": 10, "bank_name": 5, "bank_name_pinyin": 3 }, name: "banks_text_index" }
    );

    // 创建 areas 集合索引
    await db.collection('areas').createIndex({ "area_code": 1 }, { unique: true });
    await db.collection('areas').createIndex({ "province": 1, "city": 1, "district": 1 });
    await db.collection('areas').createIndex({ "parent_code": 1 });
    await db.collection('areas').createIndex({ "level": 1 });
    await db.collection('areas').createIndex(
      { "province": "text", "city": "text", "district": "text", "pinyin": "text" }
    );

    // 创建 bank_branches 集合索引
    await db.collection('bank_branches').createIndex({ "bank_no": 1 }, { unique: true });
    await db.collection('bank_branches').createIndex({ "bank_code": 1 });
    await db.collection('bank_branches').createIndex({ "area_code": 1 });
    await db.collection('bank_branches').createIndex({ "branch_name": 1 });
    await db.collection('bank_branches').createIndex({ "bank.bank_short_name": 1 });
    await db.collection('bank_branches').createIndex({ "area.province": 1, "area.city": 1, "area.district": 1 });
    await db.collection('bank_branches').createIndex({ "status": 1 });
    await db.collection('bank_branches').createIndex({ "bank_code": 1, "area.province": 1, "area.city": 1 });
    await db.collection('bank_branches').createIndex(
      {
        "bank.bank_name": "text",
        "bank.bank_short_name": "text",
        "branch_name": "text",
        "branch_full_name": "text",
        "address": "text",
        "search_text": "text"
      },
      {
        weights: {
          "bank.bank_short_name": 10,
          "branch_name": 8,
          "address": 5,
          "search_text": 3
        },
        name: "bank_branches_text_index"
      }
    );

    console.log('Indexes created successfully');
  } catch (error) {
    console.error('Error creating indexes:', error);
    throw error;
  }
}

// 验证导入结果
async function validateImport(db) {
  console.log('Validating import results...');

  try {
    // 验证记录数量
    const banksCount = await db.collection('banks').countDocuments();
    const areasCount = await db.collection('areas').countDocuments();
    const branchesCount = await db.collection('bank_branches').countDocuments();

    console.log(`Validation results:`);
    console.log(`- Banks: ${banksCount} records`);
    console.log(`- Areas: ${areasCount} records`);
    console.log(`- Branches: ${branchesCount} records`);

    // 验证必填字段
    const missingBankNo = await db.collection('bank_branches').countDocuments({ bank_no: { $exists: false } });
    const missingBankCode = await db.collection('bank_branches').countDocuments({ bank_code: { $exists: false } });
    const missingBranchName = await db.collection('bank_branches').countDocuments({ branch_name: { $exists: false } });

    console.log(`- Missing bank_no: ${missingBankNo} records`);
    console.log(`- Missing bank_code: ${missingBankCode} records`);
    console.log(`- Missing branch_name: ${missingBranchName} records`);

    return {
      banksCount,
      areasCount,
      branchesCount,
      missingBankNo,
      missingBankCode,
      missingBranchName
    };
  } catch (error) {
    console.error('Error validating import:', error);
    throw error;
  }
}

// 主函数
async function importData() {
  console.log('Starting data import...');
  console.log(`MongoDB URI: ${config.mongoUri}`);
  console.log(`Database: ${config.dbName}`);
  console.log(`CSV File: ${config.csvFile}`);

  // 检查CSV文件是否存在
  if (!fs.existsSync(config.csvFile)) {
    console.error(`Error: CSV file not found at ${config.csvFile}`);
    return;
  }

  const client = new MongoClient(config.mongoUri);

  try {
    // 连接数据库
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(config.dbName);

    // 清空现有集合
    await db.collection('banks').deleteMany({});
    await db.collection('areas').deleteMany({});
    await db.collection('bank_branches').deleteMany({});
    console.log('Existing collections cleared');

    // 读取CSV文件
    const banks = new Map();
    const areas = new Map();
    const branches = [];

    let rowCount = 0;

    // 处理CSV数据
    await new Promise((resolve, reject) => {
      fs.createReadStream(config.csvFile)
        .pipe(csv())
        .on('data', (row) => {
          try {
            rowCount++;

            // 处理银行数据
            if (row.bank_code && row.bank_name && !banks.has(row.bank_code)) {
              const bankNamePinyin = generatePinyin(row.bank_short_name || row.bank_name);
              const bankNameInitials = generatePinyinInitials(row.bank_short_name || row.bank_name);

              banks.set(row.bank_code, {
                bank_code: row.bank_code,
                bank_name: row.bank_name,
                bank_short_name: row.bank_short_name || row.bank_name,
                bank_name_pinyin: bankNamePinyin,
                bank_name_initials: bankNameInitials,
                bank_type: getBankType(row.bank_code),
                status: 1,
                created_at: new Date(),
                updated_at: new Date()
              });
            }

            // 处理地区数据
            if (row.area_code && row.province && !areas.has(row.area_code)) {
              const provinceCode = row.area_code.substring(0, 2);
              const cityCode = row.area_code.substring(0, 4);

              areas.set(row.area_code, {
                area_code: row.area_code,
                province: row.province,
                province_code: provinceCode,
                city: row.city || row.province,
                city_code: cityCode,
                district: row.district || '',
                level: row.district ? 3 : (row.city ? 2 : 1),
                parent_code: row.district ? cityCode : (row.city ? provinceCode : ''),
                pinyin: generatePinyin(row.province + (row.city || '') + (row.district || '')),
                created_at: new Date(),
                updated_at: new Date()
              });
            }

            // 处理支行数据
            if (row.bank_no && row.bank_code && row.bank_name) {
              const branchNamePinyin = generatePinyin(row.branch_name_clean || row.branch_name);
              const addressKeywords = extractAddressKeywords(row.address);

              branches.push({
                bank_no: row.bank_no,
                bank_code: row.bank_code,
                area_code: row.area_code,
                branch_code: row.branch_code,
                branch_name: row.branch_name_clean || row.branch_name,
                branch_full_name: row.full_name,
                branch_name_pinyin: branchNamePinyin,
                bank: {
                  bank_code: row.bank_code,
                  bank_name: row.bank_name,
                  bank_short_name: row.bank_short_name || row.bank_name
                },
                area: {
                  province: row.province || '',
                  city: row.city || '',
                  district: row.district || ''
                },
                address: row.address || '',
                address_keywords: addressKeywords,
                telephone: row.tel || '',
                status: 1,
                remark: row.branch_remark || '',
                search_text: generateSearchText(row),
                created_at: new Date(),
                updated_at: new Date()
              });
            }

            // 定期输出进度
            if (rowCount % config.logInterval === 0) {
              console.log(`Processed ${rowCount} rows, found ${banks.size} banks, ${areas.size} areas, ${branches.length} branches`);
            }
          } catch (error) {
            console.error('Error processing row:', error);
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`CSV processing completed. Processed ${rowCount} rows.`);
    console.log(`Found ${banks.size} banks, ${areas.size} areas, ${branches.length} branches`);

    // 导入银行数据
    if (banks.size > 0) {
      await db.collection('banks').insertMany(Array.from(banks.values()));
      console.log(`Imported ${banks.size} banks`);
    }

    // 导入地区数据
    if (areas.size > 0) {
      await db.collection('areas').insertMany(Array.from(areas.values()));
      console.log(`Imported ${areas.size} areas`);
    }

    // 分批导入支行数据
    if (branches.length > 0) {
      for (let i = 0; i < branches.length; i += config.batchSize) {
        const batch = branches.slice(i, i + config.batchSize);
        await db.collection('bank_branches').insertMany(batch);
        console.log(`Imported ${i + batch.length} of ${branches.length} branches`);
      }
    }

    // 创建索引
    await createIndexes(db);

    // 验证导入结果
    await validateImport(db);

    console.log('Data import completed successfully');
  } catch (error) {
    console.error('Error importing data:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// 执行导入
importData().catch(console.error);
