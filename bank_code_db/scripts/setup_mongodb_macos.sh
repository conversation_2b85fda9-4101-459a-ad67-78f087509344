#!/bin/bash

# MongoDB 安装和配置脚本 - macOS
# 银行联行号查询系统

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以 root 用户身份运行此脚本"
        exit 1
    fi
}

# 检查 MongoDB 安装包
check_mongodb_package() {
    log_info "检查 MongoDB 安装包..."

    if [ ! -d "/usr/local/mongodb" ]; then
        log_error "MongoDB 安装目录 /usr/local/mongodb 不存在"
        exit 1
    fi

    cd /usr/local/mongodb

    # 检查是否有 bin 目录（表示 MongoDB 已经解压）
    if [ -d "bin" ]; then
        log_info "找到已解压的 MongoDB（直接在 /usr/local/mongodb）"

        # 创建符号链接指向当前目录
        if [ -L "current" ]; then
            rm current
        fi
        ln -s . current

        log_success "MongoDB 已准备就绪"
        return 0
    fi

    # 查找 MongoDB 压缩包或已解压的目录
    if ls mongodb-macos-x86_64-*.tgz 1> /dev/null 2>&1; then
        MONGODB_ARCHIVE=$(ls mongodb-macos-x86_64-*.tgz | head -1)
        log_info "找到 MongoDB 压缩包: $MONGODB_ARCHIVE"

        # 解压
        log_info "解压 MongoDB..."
        tar -xzf "$MONGODB_ARCHIVE"

        # 获取解压后的目录名
        MONGODB_DIR=$(ls -d mongodb-macos-x86_64-*/ | head -1 | sed 's/\/$//')

    elif ls -d mongodb-macos-x86_64-*/ 1> /dev/null 2>&1; then
        MONGODB_DIR=$(ls -d mongodb-macos-x86_64-*/ | head -1 | sed 's/\/$//')
        log_info "找到已解压的 MongoDB 目录: $MONGODB_DIR"
    else
        log_error "未找到 MongoDB 安装包或目录"
        log_info "当前目录内容："
        ls -la
        exit 1
    fi

    # 创建符号链接
    if [ -L "current" ]; then
        rm current
    fi
    ln -s "$MONGODB_DIR" current

    log_success "MongoDB 安装包准备完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建 MongoDB 目录..."

    # 创建数据目录
    sudo mkdir -p /usr/local/var/mongodb
    sudo chown $USER /usr/local/var/mongodb

    # 创建日志目录
    sudo mkdir -p /usr/local/var/log/mongodb
    sudo chown $USER /usr/local/var/log/mongodb

    # 创建配置目录
    sudo mkdir -p /usr/local/etc

    # 创建 PID 目录
    sudo mkdir -p /usr/local/var/run
    sudo chown $USER /usr/local/var/run

    log_success "目录创建完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."

    # 检测 shell 类型
    if [[ $SHELL == *"zsh"* ]]; then
        SHELL_RC="$HOME/.zshrc"
    elif [[ $SHELL == *"bash"* ]]; then
        SHELL_RC="$HOME/.bash_profile"
    else
        SHELL_RC="$HOME/.profile"
    fi

    # 检查是否已经配置
    if ! grep -q "MONGODB_HOME" "$SHELL_RC" 2>/dev/null; then
        echo "" >> "$SHELL_RC"
        echo "# MongoDB Environment Variables" >> "$SHELL_RC"
        echo "export MONGODB_HOME=/usr/local/mongodb/current" >> "$SHELL_RC"
        echo "export PATH=\$MONGODB_HOME/bin:\$PATH" >> "$SHELL_RC"

        log_success "环境变量已添加到 $SHELL_RC"
        log_warning "请运行 'source $SHELL_RC' 或重新打开终端以使环境变量生效"
    else
        log_info "环境变量已存在，跳过配置"
    fi

    # 临时设置环境变量
    export MONGODB_HOME=/usr/local/mongodb/current
    export PATH=$MONGODB_HOME/bin:$PATH
}

# 复制配置文件
setup_config() {
    log_info "设置 MongoDB 配置文件..."

    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    CONFIG_SOURCE="$SCRIPT_DIR/../config/mongod-macos.conf"
    CONFIG_TARGET="/usr/local/etc/mongod.conf"

    if [ -f "$CONFIG_SOURCE" ]; then
        sudo cp "$CONFIG_SOURCE" "$CONFIG_TARGET"
        log_success "配置文件已复制到 $CONFIG_TARGET"
    else
        log_error "配置文件 $CONFIG_SOURCE 不存在"
        exit 1
    fi
}

# 启动 MongoDB
start_mongodb() {
    log_info "启动 MongoDB 服务..."

    # 检查是否已经在运行
    if pgrep mongod > /dev/null; then
        log_warning "MongoDB 已在运行"
        return 0
    fi

    # 启动 MongoDB
    mongod --config /usr/local/etc/mongod.conf

    # 等待启动
    sleep 3

    # 检查是否启动成功
    if pgrep mongod > /dev/null; then
        log_success "MongoDB 启动成功"
    else
        log_error "MongoDB 启动失败，请检查日志文件 /usr/local/var/log/mongodb/mongod.log"
        exit 1
    fi
}

# 验证安装
verify_installation() {
    log_info "验证 MongoDB 安装..."

    # 检查 MongoDB 版本
    if command -v mongod &> /dev/null; then
        VERSION=$(mongod --version | head -1)
        log_success "MongoDB 版本: $VERSION"
    else
        log_error "mongod 命令不可用，请检查环境变量配置"
        exit 1
    fi

    # 检查 MongoDB Shell
    if command -v mongosh &> /dev/null; then
        SHELL_VERSION=$(mongosh --version)
        log_success "MongoDB Shell 版本: $SHELL_VERSION"
    elif command -v mongo &> /dev/null; then
        SHELL_VERSION=$(mongo --version | head -1)
        log_success "MongoDB Shell 版本: $SHELL_VERSION"
    else
        log_warning "MongoDB Shell 不可用"
    fi

    # 测试连接
    log_info "测试 MongoDB 连接..."
    sleep 2

    if command -v mongosh &> /dev/null; then
        if mongosh --eval "db.runCommand('ping')" --quiet; then
            log_success "MongoDB 连接测试成功"
        else
            log_error "MongoDB 连接测试失败"
            exit 1
        fi
    elif command -v mongo &> /dev/null; then
        if mongo --eval "db.runCommand('ping')" --quiet; then
            log_success "MongoDB 连接测试成功"
        else
            log_error "MongoDB 连接测试失败"
            exit 1
        fi
    fi
}

# 创建启动脚本
create_startup_script() {
    log_info "创建 MongoDB 启动脚本..."

    cat > /usr/local/bin/mongodb-start << 'EOF'
#!/bin/bash
# MongoDB 启动脚本

if pgrep mongod > /dev/null; then
    echo "MongoDB 已在运行"
    exit 0
fi

echo "启动 MongoDB..."
mongod --config /usr/local/etc/mongod.conf

if [ $? -eq 0 ]; then
    echo "MongoDB 启动成功"
else
    echo "MongoDB 启动失败"
    exit 1
fi
EOF

    cat > /usr/local/bin/mongodb-stop << 'EOF'
#!/bin/bash
# MongoDB 停止脚本

if ! pgrep mongod > /dev/null; then
    echo "MongoDB 未运行"
    exit 0
fi

echo "停止 MongoDB..."
mongod --shutdown --config /usr/local/etc/mongod.conf

if [ $? -eq 0 ]; then
    echo "MongoDB 已停止"
else
    echo "MongoDB 停止失败"
    exit 1
fi
EOF

    chmod +x /usr/local/bin/mongodb-start
    chmod +x /usr/local/bin/mongodb-stop

    log_success "启动脚本创建完成"
    log_info "使用 'mongodb-start' 启动 MongoDB"
    log_info "使用 'mongodb-stop' 停止 MongoDB"
}

# 显示下一步操作
show_next_steps() {
    log_success "MongoDB 安装和配置完成！"
    echo ""
    echo "下一步操作："
    echo "1. 重新加载环境变量："
    echo "   source ~/.zshrc  # 或 source ~/.bash_profile"
    echo ""
    echo "2. 初始化银行联行号查询系统数据库："
    echo "   cd $(dirname "$0")"
    echo "   node init_database.js"
    echo ""
    echo "3. 导入银行联行号数据："
    echo "   node import_data.js"
    echo ""
    echo "4. 验证数据库："
    echo "   node verify_database.js"
    echo ""
    echo "MongoDB 管理命令："
    echo "- 启动: mongodb-start"
    echo "- 停止: mongodb-stop"
    echo "- 连接: mongosh (或 mongo)"
    echo "- 日志: tail -f /usr/local/var/log/mongodb/mongod.log"
}

# 主函数
main() {
    log_info "开始安装和配置 MongoDB for 银行联行号查询系统"

    check_root
    check_mongodb_package
    create_directories
    setup_environment
    setup_config
    start_mongodb
    verify_installation
    create_startup_script
    show_next_steps
}

# 执行主函数
main "$@"
