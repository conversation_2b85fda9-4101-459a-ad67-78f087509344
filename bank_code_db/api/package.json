{"name": "bank-code-api", "version": "1.0.0", "description": "银行联行号查询系统 RESTful API 服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "docs": "swagger-jsdoc -d swaggerDef.js src/routes/*.js -o docs/swagger.json", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["bank", "bank-code", "api", "mongodb", "express", "nodejs"], "author": "Bank Code DB Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "ejs": "^3.1.10", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.3.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pinyin-pro": "^3.18.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/bank-code-api.git"}, "bugs": {"url": "https://github.com/your-org/bank-code-api/issues"}, "homepage": "https://github.com/your-org/bank-code-api#readme"}