<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || '银行联行号查询系统 - 管理后台' %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white !important;
            border-radius: 0.375rem;
        }
        .card-stats {
            border-left: 4px solid #0d6efd;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }
        .search-box {
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-3">
                        <i class="bi bi-bank"></i>
                        银行联行号管理
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin">
                                <i class="bi bi-speedometer2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'banks' ? 'active' : '' %>" href="/admin/banks">
                                <i class="bi bi-building"></i>
                                银行管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'areas' ? 'active' : '' %>" href="/admin/areas">
                                <i class="bi bi-geo-alt"></i>
                                地区管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'branches' ? 'active' : '' %>" href="/admin/branches">
                                <i class="bi bi-shop"></i>
                                支行管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'import' ? 'active' : '' %>" href="/admin/import">
                                <i class="bi bi-upload"></i>
                                数据导入
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'settings' ? 'active' : '' %>" href="/admin/settings">
                                <i class="bi bi-gear"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>
                    
                    <hr>
                    
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i>
                            <strong><%= admin ? admin.username : 'Admin' %></strong>
                        </a>
                        <ul class="dropdown-menu text-small shadow">
                            <li><a class="dropdown-item" href="/admin/settings">设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/logout">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><%= pageTitle || '管理后台' %></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 页面内容 -->
                <%- body %>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动隐藏提示消息
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (alert.classList.contains('alert-success')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }
            });
        }, 3000);

        // 确认删除
        function confirmDelete(message) {
            return confirm(message || '确定要删除这条记录吗？');
        }

        // 格式化数字
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 格式化所有数字
            const numberElements = document.querySelectorAll('.format-number');
            numberElements.forEach(function(el) {
                el.textContent = formatNumber(parseInt(el.textContent));
            });
        });
    </script>
</body>
</html>
