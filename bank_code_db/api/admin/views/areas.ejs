<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地区管理 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        .sidebar-width { width: 280px; }
        .content-margin { margin-left: 280px; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 侧边栏导航 -->
    <div class="fixed inset-y-0 left-0 sidebar-width bg-white shadow-lg z-50">
        <!-- Logo区域 -->
        <div class="flex items-center px-6 py-4 border-b border-gray-200">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center">
                <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
            </div>
            <div class="ml-3">
                <h1 class="text-lg font-semibold text-black">银行联行号</h1>
                <p class="text-sm text-gray-600">管理系统</p>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="mt-6 px-3">
            <div class="space-y-1">
                <a href="/admin" class="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                    <svg class="hero-icon mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                    </svg>
                    仪表板
                </a>

                <a href="/admin/banks" class="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                    <svg class="hero-icon mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    银行管理
                </a>

                <a href="/admin/areas" class="bg-green-50 text-green-700 group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                    <svg class="hero-icon mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    </svg>
                    地区管理
                </a>

                <a href="/admin/branches" class="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                    <svg class="hero-icon mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    支行管理
                </a>

                <a href="/admin/import" class="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                    <svg class="hero-icon mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                    数据导入
                </a>

                <a href="/admin/settings" class="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                    <svg class="hero-icon mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    系统设置
                </a>
            </div>
        </nav>

        <!-- 用户信息 -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium text-black"><%= admin.username %></p>
                    <p class="text-xs text-gray-600">管理员</p>
                </div>
                <a href="/admin/logout" class="p-1 rounded hover:bg-gray-100 transition-colors">
                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-margin">
        <!-- 顶部栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-black">地区管理</h1>
                        <p class="text-gray-600">管理地区信息和行政层级</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <%= areas.length %> 个地区
                        </span>
                        <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg class="hero-icon mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            添加地区
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选栏 -->
        <div class="bg-white border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between space-x-4">
                    <div class="flex-1 max-w-lg">
                        <div class="relative">
                            <input type="text" placeholder="搜索地区名称或代码..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-black placeholder-gray-500">
                            <svg class="hero-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500" id="levelFilter">
                            <option value="">所有级别</option>
                            <option value="1">省份</option>
                            <option value="2">城市</option>
                            <option value="3">区县</option>
                        </select>
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500" id="provinceFilter">
                            <option value="">所有省份</option>
                            <option value="北京">北京</option>
                            <option value="上海">上海</option>
                            <option value="广东">广东</option>
                            <option value="江苏">江苏</option>
                            <option value="浙江">浙江</option>
                            <option value="山东">山东</option>
                            <option value="河南">河南</option>
                            <option value="四川">四川</option>
                            <option value="湖北">湖北</option>
                            <option value="湖南">湖南</option>
                        </select>
                        <button class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="hero-icon mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"/>
                            </svg>
                            筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地区数据表格 -->
        <div class="bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                地区信息
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                地区代码
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                行政级别
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                上级地区
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">操作</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <% areas.forEach(function(area, index) { %>
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                                <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-black">
                                                <%= area.province %><% if (area.city && area.city !== area.province) { %> · <%= area.city %><% } %><% if (area.district && area.district !== area.city) { %> · <%= area.district %><% } %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <% if (area.level === 1) { %>
                                                    省级行政区
                                                <% } else if (area.level === 2) { %>
                                                    地级行政区
                                                <% } else if (area.level === 3) { %>
                                                    县级行政区
                                                <% } else { %>
                                                    未知级别
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 font-mono">
                                        <%= area.area_code %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <%
                                        let levelText = '';
                                        let levelColor = '';
                                        if (area.level === 1) {
                                            levelText = '省份';
                                            levelColor = 'bg-red-100 text-red-800';
                                        } else if (area.level === 2) {
                                            levelText = '城市';
                                            levelColor = 'bg-blue-100 text-blue-800';
                                        } else if (area.level === 3) {
                                            levelText = '区县';
                                            levelColor = 'bg-green-100 text-green-800';
                                        } else {
                                            levelText = '未知';
                                            levelColor = 'bg-gray-100 text-gray-800';
                                        }
                                    %>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= levelColor %>">
                                        <%= levelText %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (area.level === 2 && area.province) { %>
                                        <%= area.province %>
                                    <% } else if (area.level === 3 && area.city) { %>
                                        <%= area.city %>
                                    <% } else { %>
                                        -
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <%= area.status || '正常' %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <button class="text-green-600 hover:text-green-900 transition-colors" title="编辑">
                                            <svg class="hero-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                                            </svg>
                                        </button>
                                        <button class="text-gray-600 hover:text-gray-900 transition-colors" title="查看详情">
                                            <svg class="hero-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </button>
                                        <button class="text-blue-600 hover:text-blue-900 transition-colors" title="查看下级">
                                            <svg class="hero-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                            </svg>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900 transition-colors" title="删除" onclick="confirmDelete('<%= area.province %><% if (area.city && area.city !== area.province) { %> · <%= area.city %><% } %><% if (area.district && area.district !== area.city) { %> · <%= area.district %><% } %>')">
                                            <svg class="hero-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页导航 -->
        <% if (pagination.pages > 1) { %>
            <div class="bg-white px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <% if (pagination.page > 1) { %>
                            <a href="?page=<%= pagination.page - 1 %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                上一页
                            </a>
                        <% } %>
                        <% if (pagination.page < pagination.pages) { %>
                            <a href="?page=<%= pagination.page + 1 %>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下一页
                            </a>
                        <% } %>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                显示第 <span class="font-medium"><%= (pagination.page - 1) * pagination.limit + 1 %></span> 到
                                <span class="font-medium"><%= Math.min(pagination.page * pagination.limit, pagination.total) %></span> 条，
                                共 <span class="font-medium"><%= pagination.total %></span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <% if (pagination.page > 1) { %>
                                    <a href="?page=<%= pagination.page - 1 %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                <% } %>

                                <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) { %>
                                    <% if (i === pagination.page) { %>
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-green-50 text-sm font-medium text-green-600">
                                            <%= i %>
                                        </span>
                                    <% } else { %>
                                        <a href="?page=<%= i %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            <%= i %>
                                        </a>
                                    <% } %>
                                <% } %>

                                <% if (pagination.page < pagination.pages) { %>
                                    <a href="?page=<%= pagination.page + 1 %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                <% } %>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        <% } %>
    </div>

    <script>
        // 搜索功能
        const searchInput = document.querySelector('input[type="text"]');
        let searchTimeout;

        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = e.target.value.trim();
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    filterTable(searchTerm);
                }
            }, 300);
        });

        // 表格筛选功能
        function filterTable(searchTerm) {
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const areaName = row.querySelector('.text-black').textContent.toLowerCase();
                const areaCode = row.querySelector('.font-mono').textContent.toLowerCase();

                if (areaName.includes(searchTerm.toLowerCase()) || areaCode.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // 行政级别筛选
        document.getElementById('levelFilter').addEventListener('change', function(e) {
            const selectedLevel = e.target.value;
            const rows = document.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const levelSpan = row.querySelectorAll('td')[2].querySelector('span');
                const levelText = levelSpan.textContent.trim();

                if (selectedLevel === '') {
                    row.style.display = '';
                } else {
                    const levelMap = { '1': '省份', '2': '城市', '3': '区县' };
                    if (levelText === levelMap[selectedLevel]) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        });

        // 省份筛选
        document.getElementById('provinceFilter').addEventListener('change', function(e) {
            const selectedProvince = e.target.value;
            const rows = document.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const areaName = row.querySelector('.text-black').textContent;

                if (selectedProvince === '' || areaName.includes(selectedProvince)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // 删除确认
        function confirmDelete(areaName) {
            if (confirm(`确定要删除地区 "${areaName}" 吗？此操作不可撤销。`)) {
                console.log('删除地区:', areaName);
                alert('删除功能开发中...');
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.3s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });

        // 查看下级地区功能
        document.querySelectorAll('[title="查看下级"]').forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('tr');
                const areaName = row.querySelector('.text-black').textContent;
                const levelSpan = row.querySelectorAll('td')[2].querySelector('span');
                const level = levelSpan.textContent.trim();

                if (level === '省份') {
                    alert(`查看 ${areaName} 下的城市列表功能开发中...`);
                } else if (level === '城市') {
                    alert(`查看 ${areaName} 下的区县列表功能开发中...`);
                } else {
                    alert('该地区没有下级行政区');
                }
            });
        });
    </script>
</body>
</html>
