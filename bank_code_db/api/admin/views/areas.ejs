<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地区管理 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        .sidebar-width { width: 280px; }
        .content-margin { margin-left: 280px; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <a href="/admin" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-lg font-semibold text-black">地区管理</h1>
            </div>
            <div class="flex items-center space-x-2">
                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    <%= areas.length %> 个地区
                </span>
            </div>
        </div>
    </div>

    <!-- 筛选标签 -->
    <div class="px-6 py-4 bg-white/50">
        <div class="flex space-x-2 overflow-x-auto">
            <button class="px-4 py-2 bg-green-500 text-white rounded-full text-sm font-medium whitespace-nowrap">
                全部
            </button>
            <button class="px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium border border-gray-200 whitespace-nowrap">
                省份
            </button>
            <button class="px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium border border-gray-200 whitespace-nowrap">
                城市
            </button>
            <button class="px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium border border-gray-200 whitespace-nowrap">
                区县
            </button>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="px-6 pb-4">
        <div class="relative">
            <input type="text" placeholder="搜索地区名称或代码..."
                   class="w-full pl-10 pr-4 py-3 bg-white rounded-2xl border border-gray-200 focus:border-green-400 focus:ring-2 focus:ring-green-100 transition-all text-black placeholder-gray-500">
            <svg class="hero-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
        </div>
    </div>

    <!-- 地区列表 -->
    <div class="px-6 pb-6 space-y-3">
        <% areas.forEach(function(area, index) { %>
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                                <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-black text-base">
                                    <%= area.province %><% if (area.city && area.city !== area.province) { %> · <%= area.city %><% } %><% if (area.district && area.district !== area.city) { %> · <%= area.district %><% } %>
                                </h3>
                                <p class="text-gray-600 text-sm">地区代码: <%= area.area_code %></p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-4 text-xs">
                            <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded-lg">
                                代码: <%= area.area_code %>
                            </span>
                            <%
                                let levelText = '';
                                let levelColor = '';
                                if (area.level === 1) {
                                    levelText = '省份';
                                    levelColor = 'bg-red-100 text-red-700';
                                } else if (area.level === 2) {
                                    levelText = '城市';
                                    levelColor = 'bg-blue-100 text-blue-700';
                                } else if (area.level === 3) {
                                    levelText = '区县';
                                    levelColor = 'bg-green-100 text-green-700';
                                }
                            %>
                            <span class="px-2 py-1 <%= levelColor %> rounded-lg">
                                <%= levelText %>
                            </span>
                        </div>
                    </div>

                    <div class="flex flex-col space-y-2">
                        <button class="p-2 bg-green-50 hover:bg-green-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                            </svg>
                        </button>
                        <button class="p-2 bg-red-50 hover:bg-red-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        <% }); %>
    </div>

    <!-- 分页导航 -->
    <% if (pagination.pages > 1) { %>
        <div class="px-6 py-4 bg-white/50 border-t border-gray-100">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    第 <%= pagination.page %> 页，共 <%= pagination.pages %> 页
                </div>
                <div class="flex space-x-2">
                    <% if (pagination.page > 1) { %>
                        <a href="?page=<%= pagination.page - 1 %>"
                           class="px-3 py-2 bg-white rounded-lg border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                            上一页
                        </a>
                    <% } %>
                    <% if (pagination.page < pagination.pages) { %>
                        <a href="?page=<%= pagination.page + 1 %>"
                           class="px-3 py-2 bg-green-500 rounded-lg text-white hover:bg-green-600 transition-colors">
                            下一页
                        </a>
                    <% } %>
                </div>
            </div>
        </div>
    <% } %>

    <!-- 浮动添加按钮 -->
    <div class="fixed bottom-6 right-6">
        <button class="w-14 h-14 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center">
            <svg class="hero-icon-lg text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
        </button>
    </div>

    <script>
        // 筛选功能
        document.querySelectorAll('button').forEach(btn => {
            if (btn.textContent.includes('全部') || btn.textContent.includes('省份') || btn.textContent.includes('城市') || btn.textContent.includes('区县')) {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的激活状态
                    document.querySelectorAll('button').forEach(b => {
                        if (b.textContent.includes('全部') || b.textContent.includes('省份') || b.textContent.includes('城市') || b.textContent.includes('区县')) {
                            b.className = 'px-4 py-2 bg-white text-gray-700 rounded-full text-sm font-medium border border-gray-200 whitespace-nowrap';
                        }
                    });

                    // 激活当前按钮
                    this.className = 'px-4 py-2 bg-green-500 text-white rounded-full text-sm font-medium whitespace-nowrap';

                    // 筛选逻辑
                    const filterType = this.textContent.trim();
                    const areaCards = document.querySelectorAll('.bg-white\\/70');

                    areaCards.forEach(card => {
                        if (filterType === '全部') {
                            card.style.display = 'block';
                        } else {
                            const levelSpan = card.querySelector('.bg-red-100, .bg-blue-100, .bg-green-100');
                            if (levelSpan && levelSpan.textContent.includes(filterType)) {
                                card.style.display = 'block';
                            } else {
                                card.style.display = 'none';
                            }
                        }
                    });
                });
            }
        });

        // 搜索功能
        document.querySelector('input[type="text"]').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const areaCards = document.querySelectorAll('.bg-white\\/70');

            areaCards.forEach(card => {
                const areaName = card.querySelector('h3').textContent.toLowerCase();
                const areaCode = card.querySelector('.text-gray-600').textContent.toLowerCase();

                if (areaName.includes(searchTerm) || areaCode.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
