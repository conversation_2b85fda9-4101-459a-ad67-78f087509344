<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行管理 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline/index.css" rel="stylesheet">
    <style>
        body { 
            width: 375px; 
            height: 812px; 
            margin: 0 auto; 
            border: 1px solid #e5e7eb; 
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        ::-webkit-scrollbar { display: none; }
        * { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <a href="/admin" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-lg font-semibold text-black">银行管理</h1>
            </div>
            <div class="flex items-center space-x-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                    <%= banks.length %> 家银行
                </span>
            </div>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="px-6 py-4 bg-white/50">
        <div class="relative">
            <input type="text" placeholder="搜索银行名称或代码..." 
                   class="w-full pl-10 pr-4 py-3 bg-white rounded-2xl border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all text-black placeholder-gray-500">
            <svg class="hero-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
        </div>
    </div>

    <!-- 银行列表 -->
    <div class="px-6 pb-6 space-y-3">
        <% banks.forEach(function(bank, index) { %>
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                <span class="text-white font-bold text-sm"><%= bank.bank_code %></span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-black text-base"><%= bank.bank_short_name %></h3>
                                <p class="text-gray-600 text-sm"><%= bank.bank_full_name %></p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4 text-xs">
                            <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded-lg">
                                代码: <%= bank.bank_code %>
                            </span>
                            <% if (bank.bank_type) { %>
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-lg">
                                    <%= bank.bank_type %>
                                </span>
                            <% } %>
                        </div>
                    </div>
                    
                    <div class="flex flex-col space-y-2">
                        <button class="p-2 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                            </svg>
                        </button>
                        <button class="p-2 bg-red-50 hover:bg-red-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        <% }); %>
    </div>

    <!-- 分页导航 -->
    <% if (pagination.pages > 1) { %>
        <div class="px-6 py-4 bg-white/50 border-t border-gray-100">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    第 <%= pagination.page %> 页，共 <%= pagination.pages %> 页
                </div>
                <div class="flex space-x-2">
                    <% if (pagination.page > 1) { %>
                        <a href="?page=<%= pagination.page - 1 %>" 
                           class="px-3 py-2 bg-white rounded-lg border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                            上一页
                        </a>
                    <% } %>
                    <% if (pagination.page < pagination.pages) { %>
                        <a href="?page=<%= pagination.page + 1 %>" 
                           class="px-3 py-2 bg-blue-500 rounded-lg text-white hover:bg-blue-600 transition-colors">
                            下一页
                        </a>
                    <% } %>
                </div>
            </div>
        </div>
    <% } %>

    <!-- 浮动添加按钮 -->
    <div class="fixed bottom-6 right-6">
        <button class="w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center">
            <svg class="hero-icon-lg text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
        </button>
    </div>

    <script>
        // 搜索功能
        document.querySelector('input[type="text"]').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const bankCards = document.querySelectorAll('.bg-white\\/70');
            
            bankCards.forEach(card => {
                const bankName = card.querySelector('h3').textContent.toLowerCase();
                const bankCode = card.querySelector('.text-gray-700').textContent.toLowerCase();
                
                if (bankName.includes(searchTerm) || bankCode.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // 删除确认
        document.querySelectorAll('.text-red-600').forEach(btn => {
            btn.closest('button').addEventListener('click', function() {
                if (confirm('确定要删除这家银行吗？')) {
                    // 删除逻辑
                    console.log('删除银行');
                }
            });
        });
    </script>
</body>
</html>
