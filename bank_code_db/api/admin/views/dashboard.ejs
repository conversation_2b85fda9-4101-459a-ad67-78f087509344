<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理仪表板 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            border: 1px solid #e5e7eb;
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        ::-webkit-scrollbar { display: none; }
        * { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                </div>
                <h1 class="text-lg font-semibold text-black">管理仪表板</h1>
            </div>
            <div class="flex items-center space-x-2">
                <a href="/admin/logout" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>

    <!-- 欢迎信息 -->
    <div class="px-6 py-4">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
            <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <svg class="hero-icon-lg text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                </div>
                <div>
                    <h2 class="text-base font-semibold text-black">欢迎回来，<%= admin.username %></h2>
                    <p class="text-gray-600 text-sm">系统运行正常，数据已同步</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="px-6 pb-4">
        <div class="grid grid-cols-2 gap-3">
            <!-- 银行统计 -->
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
                <div class="flex items-center justify-between mb-2">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-500">+2.5%</span>
                </div>
                <div class="text-2xl font-bold text-black mb-1"><%= stats.banks %></div>
                <div class="text-xs text-gray-600">银行总数</div>
            </div>

            <!-- 地区统计 -->
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
                <div class="flex items-center justify-between mb-2">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-500">+1.2%</span>
                </div>
                <div class="text-2xl font-bold text-black mb-1"><%= Math.floor(stats.areas / 1000) %>K</div>
                <div class="text-xs text-gray-600">地区总数</div>
            </div>

            <!-- 支行统计 -->
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
                <div class="flex items-center justify-between mb-2">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-500">+5.8%</span>
                </div>
                <div class="text-2xl font-bold text-black mb-1"><%= Math.floor(stats.branches / 1000) %>K</div>
                <div class="text-xs text-gray-600">支行总数</div>
            </div>

            <!-- 查询统计 -->
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
                <div class="flex items-center justify-between mb-2">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <span class="text-xs text-gray-500">+12.3%</span>
                </div>
                <div class="text-2xl font-bold text-black mb-1"><%= stats.queryLogs %></div>
                <div class="text-xs text-gray-600">查询次数</div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="px-6 pb-4">
        <h3 class="text-base font-semibold text-black mb-3">快速操作</h3>
        <div class="grid grid-cols-2 gap-3">
            <a href="/admin/banks" class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300 block">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                        <svg class="hero-icon text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-black text-sm">银行管理</p>
                        <p class="text-gray-600 text-xs">管理银行信息</p>
                    </div>
                </div>
            </a>

            <a href="/admin/areas" class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300 block">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                        <svg class="hero-icon text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-black text-sm">地区管理</p>
                        <p class="text-gray-600 text-xs">管理地区信息</p>
                    </div>
                </div>
            </a>

            <a href="/admin/branches" class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300 block">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                        <svg class="hero-icon text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-black text-sm">支行管理</p>
                        <p class="text-gray-600 text-xs">管理支行信息</p>
                    </div>
                </div>
            </a>

            <a href="/admin/import" class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300 block">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                        <svg class="hero-icon text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-black text-sm">数据导入</p>
                        <p class="text-gray-600 text-xs">导入CSV数据</p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="px-6 pb-4">
        <h3 class="text-base font-semibold text-black mb-3">系统状态</h3>
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-black">API 服务</span>
                    </div>
                    <span class="text-xs text-green-600 font-medium">运行正常</span>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-black">数据库连接</span>
                    </div>
                    <span class="text-xs text-green-600 font-medium">连接正常</span>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span class="text-sm text-black">数据同步</span>
                    </div>
                    <span class="text-xs text-blue-600 font-medium">已同步</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近活动 -->
    <div class="px-6 pb-6">
        <h3 class="text-base font-semibold text-black mb-3">最近活动</h3>
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-100">
            <% if (recentLogs && recentLogs.length > 0) { %>
                <div class="p-4 space-y-3">
                    <% recentLogs.slice(0, 3).forEach(function(log, index) { %>
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-black"><%= log.query_type || '数据查询' %></p>
                                <p class="text-xs text-gray-600"><%= new Date(log.created_at).toLocaleString('zh-CN') %></p>
                            </div>
                            <span class="text-xs text-gray-500">结果: <%= log.result_count || 0 %></span>
                        </div>
                    <% }); %>
                </div>
                <div class="p-4 border-t border-gray-100">
                    <button class="w-full text-center text-sm text-gray-600 hover:text-gray-800 transition-colors">
                        查看更多活动
                    </button>
                </div>
            <% } else { %>
                <div class="p-8 text-center">
                    <svg class="w-12 h-12 mx-auto text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                    </svg>
                    <p class="text-gray-500 text-sm">暂无活动记录</p>
                </div>
            <% } %>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white/80 backdrop-blur-lg border-t border-gray-100">
        <div class="flex items-center justify-around py-3">
            <a href="/admin" class="flex flex-col items-center space-y-1 text-blue-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                </svg>
                <span class="text-xs">仪表板</span>
            </a>

            <a href="/admin/banks" class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <span class="text-xs">银行</span>
            </a>

            <a href="/admin/branches" class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <span class="text-xs">支行</span>
            </a>

            <a href="/admin/settings" class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                <span class="text-xs">设置</span>
            </a>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.bg-white\\/70');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 实时更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN');
            // 可以在这里更新页面上的时间显示
        }

        setInterval(updateTime, 1000);
    </script>
</body>
</html>
