<%
    const currentPage = 'dashboard';
    const pageTitle = '仪表板';
%>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            银行总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800 format-number">
                            <%= stats.banks %>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2" style="border-left: 4px solid #28a745;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            地区总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800 format-number">
                            <%= stats.areas %>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-geo-alt text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2" style="border-left: 4px solid #17a2b8;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            支行总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800 format-number">
                            <%= stats.branches %>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-shop text-info" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2" style="border-left: 4px solid #ffc107;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            查询日志
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800 format-number">
                            <%= stats.queryLogs %>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning"></i>
                    快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/admin/banks" class="btn btn-outline-primary btn-block w-100">
                            <i class="bi bi-building"></i>
                            管理银行
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/areas" class="btn btn-outline-success btn-block w-100">
                            <i class="bi bi-geo-alt"></i>
                            管理地区
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/branches" class="btn btn-outline-info btn-block w-100">
                            <i class="bi bi-shop"></i>
                            管理支行
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/import" class="btn btn-outline-warning btn-block w-100">
                            <i class="bi bi-upload"></i>
                            导入数据
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近查询日志 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history"></i>
                    最近查询日志
                </h6>
                <small class="text-muted">最近 10 条记录</small>
            </div>
            <div class="card-body">
                <% if (recentLogs && recentLogs.length > 0) { %>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>查询类型</th>
                                    <th>查询内容</th>
                                    <th>结果数量</th>
                                    <th>IP地址</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% recentLogs.forEach(function(log) { %>
                                    <tr>
                                        <td>
                                            <small><%= new Date(log.created_at).toLocaleString('zh-CN') %></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><%= log.query_type || '未知' %></span>
                                        </td>
                                        <td>
                                            <code><%= log.query_content || '无' %></code>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><%= log.result_count || 0 %></span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><%= log.ip_address || '未知' %></small>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem; color: #6c757d;"></i>
                        <p class="text-muted mt-2">暂无查询日志</p>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-info-circle"></i>
                    系统信息
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>系统版本:</strong></td>
                        <td>v1.0.0</td>
                    </tr>
                    <tr>
                        <td><strong>数据库:</strong></td>
                        <td>MongoDB</td>
                    </tr>
                    <tr>
                        <td><strong>运行环境:</strong></td>
                        <td>Node.js</td>
                    </tr>
                    <tr>
                        <td><strong>最后更新:</strong></td>
                        <td><%= new Date().toLocaleString('zh-CN') %></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-link-45deg"></i>
                    快速链接
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="http://localhost:3000/docs" target="_blank" class="list-group-item list-group-item-action">
                        <i class="bi bi-book"></i>
                        API 文档
                    </a>
                    <a href="http://localhost:3000/health" target="_blank" class="list-group-item list-group-item-action">
                        <i class="bi bi-heart-pulse"></i>
                        健康检查
                    </a>
                    <a href="http://localhost:3000/v1/branches/stats" target="_blank" class="list-group-item list-group-item-action">
                        <i class="bi bi-graph-up"></i>
                        API 统计
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 这里可以添加仪表板特定的JavaScript代码
        console.log('仪表板加载完成');
    });
</script>
