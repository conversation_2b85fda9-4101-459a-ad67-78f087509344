<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            width: 375px; 
            height: 812px; 
            margin: 0 auto; 
            border: 1px solid #e5e7eb; 
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        ::-webkit-scrollbar { display: none; }
        * { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-slate-50 to-zinc-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <a href="/admin" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-lg font-semibold text-black">系统设置</h1>
            </div>
            <div class="flex items-center space-x-2">
                <span class="px-3 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                    管理员
                </span>
            </div>
        </div>
    </div>

    <!-- 用户信息 -->
    <div class="px-6 py-4">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-br from-gray-500 to-slate-600 rounded-2xl flex items-center justify-center">
                    <svg class="hero-icon-lg text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-black"><%= admin.username %></h2>
                    <p class="text-gray-600 text-sm">系统管理员</p>
                    <p class="text-gray-500 text-xs mt-1">最后登录: <%= new Date().toLocaleString('zh-CN') %></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置选项 -->
    <div class="px-6 pb-6 space-y-4">
        <!-- 系统配置 -->
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-100">
            <div class="p-4 border-b border-gray-100">
                <h3 class="font-semibold text-black">系统配置</h3>
            </div>
            
            <div class="p-4 space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-black text-sm">API 服务状态</p>
                            <p class="text-gray-600 text-xs">当前运行正常</p>
                        </div>
                    </div>
                    <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-black text-sm">数据库连接</p>
                            <p class="text-gray-600 text-xs">MongoDB 连接正常</p>
                        </div>
                    </div>
                    <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-black text-sm">安全设置</p>
                            <p class="text-gray-600 text-xs">API 密钥保护已启用</p>
                        </div>
                    </div>
                    <button class="text-gray-400">
                        <svg class="hero-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据管理 -->
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-100">
            <div class="p-4 border-b border-gray-100">
                <h3 class="font-semibold text-black">数据管理</h3>
            </div>
            
            <div class="p-4 space-y-3">
                <button class="w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <p class="font-medium text-black text-sm">数据备份</p>
                            <p class="text-gray-600 text-xs">导出系统数据</p>
                        </div>
                    </div>
                    <svg class="hero-icon text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
                
                <button class="w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-xl transition-colors">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <p class="font-medium text-black text-sm">数据同步</p>
                            <p class="text-gray-600 text-xs">同步最新数据</p>
                        </div>
                    </div>
                    <svg class="hero-icon text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
                
                <button class="w-full flex items-center justify-between p-3 bg-red-50 hover:bg-red-100 rounded-xl transition-colors">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <p class="font-medium text-black text-sm">清理缓存</p>
                            <p class="text-gray-600 text-xs">清理系统缓存</p>
                        </div>
                    </div>
                    <svg class="hero-icon text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-100">
            <div class="p-4 border-b border-gray-100">
                <h3 class="font-semibold text-black">系统信息</h3>
            </div>
            
            <div class="p-4 space-y-3 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">系统版本</span>
                    <span class="text-black font-medium">v1.0.0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Node.js 版本</span>
                    <span class="text-black font-medium">v18.0.0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">数据库版本</span>
                    <span class="text-black font-medium">MongoDB 6.0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">运行时间</span>
                    <span class="text-black font-medium">2小时30分</span>
                </div>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="pt-4">
            <a href="/admin/logout" 
               class="w-full flex items-center justify-center space-x-2 py-4 bg-red-500 hover:bg-red-600 text-white rounded-2xl font-medium transition-colors">
                <svg class="hero-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                <span>退出登录</span>
            </a>
        </div>
    </div>

    <script>
        // 数据备份
        document.querySelector('button[class*="bg-blue-50"]').addEventListener('click', function() {
            if (confirm('确定要备份系统数据吗？')) {
                alert('数据备份功能开发中...');
            }
        });

        // 数据同步
        document.querySelector('button[class*="bg-green-50"]').addEventListener('click', function() {
            if (confirm('确定要同步最新数据吗？')) {
                alert('数据同步功能开发中...');
            }
        });

        // 清理缓存
        document.querySelector('button[class*="bg-red-50"]').addEventListener('click', function() {
            if (confirm('确定要清理系统缓存吗？')) {
                alert('缓存清理功能开发中...');
            }
        });
    </script>
</body>
</html>
