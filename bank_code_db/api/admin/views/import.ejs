<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据导入 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            width: 375px; 
            height: 812px; 
            margin: 0 auto; 
            border: 1px solid #e5e7eb; 
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        .hero-icon-xl { width: 32px; height: 32px; }
        ::-webkit-scrollbar { display: none; }
        * { -ms-overflow-style: none; scrollbar-width: none; }
        .upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <a href="/admin" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-lg font-semibold text-black">数据导入</h1>
            </div>
            <div class="flex items-center space-x-2">
                <span class="px-3 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                    CSV 导入
                </span>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <% if (message) { %>
        <div class="mx-6 mt-4">
            <div class="p-4 rounded-2xl <%= message.type === 'success' ? 'bg-green-100 border border-green-200' : 'bg-red-100 border border-red-200' %>">
                <div class="flex items-center space-x-3">
                    <% if (message.type === 'success') { %>
                        <svg class="hero-icon text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    <% } else { %>
                        <svg class="hero-icon text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    <% } %>
                    <p class="text-sm font-medium <%= message.type === 'success' ? 'text-green-800' : 'text-red-800' %>">
                        <%= message.text %>
                    </p>
                </div>
            </div>
        </div>
    <% } %>

    <!-- 导入说明 -->
    <div class="px-6 py-4">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
            <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center">
                    <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h2 class="text-base font-semibold text-black">导入说明</h2>
            </div>
            
            <div class="space-y-2 text-sm text-gray-600">
                <p>• 支持 CSV 格式文件导入</p>
                <p>• 文件大小限制：50MB</p>
                <p>• 支持银行、地区、支行数据</p>
                <p>• 请确保数据格式正确</p>
            </div>
        </div>
    </div>

    <!-- 文件上传区域 -->
    <div class="px-6 pb-4">
        <form action="/admin/import" method="POST" enctype="multipart/form-data" id="uploadForm">
            <div class="upload-area bg-white/70 backdrop-blur-sm rounded-2xl p-8 text-center border border-gray-100" id="uploadArea">
                <div class="mb-4">
                    <svg class="hero-icon-xl mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                </div>
                
                <h3 class="text-lg font-semibold text-black mb-2">选择 CSV 文件</h3>
                <p class="text-gray-600 text-sm mb-4">拖拽文件到此处或点击选择文件</p>
                
                <input type="file" name="csvFile" accept=".csv" class="hidden" id="fileInput" required>
                
                <button type="button" onclick="document.getElementById('fileInput').click()" 
                        class="px-6 py-3 bg-gradient-to-r from-orange-500 to-amber-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-300">
                    选择文件
                </button>
                
                <div class="mt-4 text-xs text-gray-500">
                    支持的格式：CSV (最大 50MB)
                </div>
            </div>
            
            <!-- 文件信息显示 -->
            <div id="fileInfo" class="hidden mt-4 bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                            <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-black" id="fileName"></p>
                            <p class="text-sm text-gray-600" id="fileSize"></p>
                        </div>
                    </div>
                    <button type="button" onclick="clearFile()" class="p-2 bg-red-50 hover:bg-red-100 rounded-xl transition-colors">
                        <svg class="hero-icon text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 上传按钮 -->
            <div class="mt-6">
                <button type="submit" id="uploadBtn" disabled
                        class="w-full py-4 bg-gray-300 text-gray-500 rounded-2xl font-medium transition-all duration-300 disabled:cursor-not-allowed">
                    开始导入
                </button>
            </div>
        </form>
    </div>

    <!-- 导入历史 -->
    <div class="px-6 pb-6">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center">
                    <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h2 class="text-base font-semibold text-black">导入历史</h2>
            </div>
            
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-green-50 rounded-xl border border-green-200">
                    <div>
                        <p class="font-medium text-green-800 text-sm">银行数据导入</p>
                        <p class="text-green-600 text-xs">2024-05-26 15:30</p>
                    </div>
                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-lg text-xs">成功</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-xl border border-blue-200">
                    <div>
                        <p class="font-medium text-blue-800 text-sm">支行数据导入</p>
                        <p class="text-blue-600 text-xs">2024-05-26 14:20</p>
                    </div>
                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-lg text-xs">成功</span>
                </div>
                
                <div class="text-center py-4">
                    <button class="text-gray-500 text-sm hover:text-gray-700 transition-colors">
                        查看更多历史记录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadForm = document.getElementById('uploadForm');

        // 拖拽上传
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 处理文件
        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                alert('请选择 CSV 格式的文件');
                return;
            }

            if (file.size > 50 * 1024 * 1024) {
                alert('文件大小不能超过 50MB');
                return;
            }

            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            
            fileInfo.classList.remove('hidden');
            uploadBtn.disabled = false;
            uploadBtn.className = 'w-full py-4 bg-gradient-to-r from-orange-500 to-amber-600 text-white rounded-2xl font-medium hover:shadow-lg transition-all duration-300';
            uploadBtn.textContent = '开始导入';
        }

        // 清除文件
        function clearFile() {
            fileInput.value = '';
            fileInfo.classList.add('hidden');
            uploadBtn.disabled = true;
            uploadBtn.className = 'w-full py-4 bg-gray-300 text-gray-500 rounded-2xl font-medium transition-all duration-300 disabled:cursor-not-allowed';
            uploadBtn.textContent = '开始导入';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 表单提交
        uploadForm.addEventListener('submit', function(e) {
            uploadBtn.disabled = true;
            uploadBtn.textContent = '导入中...';
            uploadBtn.className = 'w-full py-4 bg-gray-300 text-gray-500 rounded-2xl font-medium transition-all duration-300 disabled:cursor-not-allowed';
        });
    </script>
</body>
</html>
