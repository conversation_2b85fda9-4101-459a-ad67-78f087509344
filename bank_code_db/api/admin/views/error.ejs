<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统错误 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            width: 375px; 
            height: 812px; 
            margin: 0 auto; 
            border: 1px solid #e5e7eb; 
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-xl { width: 48px; height: 48px; }
        ::-webkit-scrollbar { display: none; }
        * { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-gradient-to-br from-red-50 via-pink-50 to-rose-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <a href="/admin" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-lg font-semibold text-black">系统错误</h1>
            </div>
        </div>
    </div>

    <!-- 错误内容 -->
    <div class="flex flex-col items-center justify-center px-6 py-12 text-center">
        <!-- 错误图标 -->
        <div class="w-24 h-24 bg-gradient-to-br from-red-500 to-pink-600 rounded-3xl flex items-center justify-center mb-6">
            <svg class="hero-icon-xl text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        </div>

        <!-- 错误标题 -->
        <h2 class="text-2xl font-bold text-black mb-3">出现错误</h2>
        
        <!-- 错误描述 -->
        <p class="text-gray-600 text-base mb-6 leading-relaxed">
            <%= error || '系统遇到了一个未知错误，请稍后重试。' %>
        </p>

        <!-- 错误详情卡片 -->
        <div class="w-full bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 mb-6">
            <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h3 class="font-semibold text-black text-sm">错误详情</h3>
            </div>
            
            <div class="text-left space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">错误时间:</span>
                    <span class="text-black"><%= new Date().toLocaleString('zh-CN') %></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">错误类型:</span>
                    <span class="text-black">系统错误</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">错误代码:</span>
                    <span class="text-black font-mono">ERR_500</span>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="w-full space-y-3">
            <button onclick="window.location.reload()" 
                    class="w-full py-4 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-2xl font-medium hover:shadow-lg transition-all duration-300">
                重新加载页面
            </button>
            
            <a href="/admin" 
               class="block w-full py-4 bg-white/70 backdrop-blur-sm border border-gray-200 text-gray-700 rounded-2xl font-medium hover:bg-white transition-all duration-300 text-center">
                返回首页
            </a>
            
            <button onclick="history.back()" 
                    class="w-full py-4 bg-gray-100 text-gray-600 rounded-2xl font-medium hover:bg-gray-200 transition-all duration-300">
                返回上一页
            </button>
        </div>

        <!-- 帮助信息 -->
        <div class="w-full mt-8 bg-blue-50 rounded-2xl p-4 border border-blue-200">
            <div class="flex items-center space-x-3 mb-2">
                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h4 class="font-semibold text-blue-800 text-sm">需要帮助？</h4>
            </div>
            
            <p class="text-blue-700 text-xs leading-relaxed">
                如果问题持续存在，请检查网络连接或联系系统管理员。您也可以尝试清除浏览器缓存后重新访问。
            </p>
        </div>
    </div>

    <script>
        // 自动重试机制
        let retryCount = 0;
        const maxRetries = 3;

        function autoRetry() {
            if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(() => {
                    console.log(`自动重试第 ${retryCount} 次...`);
                    window.location.reload();
                }, 5000);
            }
        }

        // 错误报告
        function reportError() {
            const errorData = {
                error: '<%= error || "未知错误" %>',
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            
            console.log('错误报告:', errorData);
            // 这里可以发送错误报告到服务器
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            reportError();
            
            // 如果是网络错误，启动自动重试
            if ('<%= error %>'.includes('网络') || '<%= error %>'.includes('连接')) {
                autoRetry();
            }
        });
    </script>
</body>
</html>
