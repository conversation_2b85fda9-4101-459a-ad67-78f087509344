<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支行管理 - 银行联行号查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            width: 375px; 
            height: 812px; 
            margin: 0 auto; 
            border: 1px solid #e5e7eb; 
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        .hero-icon { width: 20px; height: 20px; }
        .hero-icon-lg { width: 24px; height: 24px; }
        ::-webkit-scrollbar { display: none; }
        * { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white/80 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <a href="/admin" class="p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition-colors">
                    <svg class="hero-icon text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-lg font-semibold text-black">支行管理</h1>
            </div>
            <div class="flex items-center space-x-2">
                <span class="px-3 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                    <%= branches.length %> 个支行
                </span>
            </div>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="px-6 py-4 bg-white/50">
        <div class="relative mb-3">
            <input type="text" placeholder="搜索支行名称、联行号或地址..." value="<%= search || '' %>"
                   class="w-full pl-10 pr-4 py-3 bg-white rounded-2xl border border-gray-200 focus:border-purple-400 focus:ring-2 focus:ring-purple-100 transition-all text-black placeholder-gray-500">
            <svg class="hero-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
        </div>
        
        <!-- 快速筛选 -->
        <div class="flex space-x-2 overflow-x-auto">
            <button class="px-3 py-1 bg-purple-500 text-white rounded-full text-xs font-medium whitespace-nowrap">
                全部
            </button>
            <button class="px-3 py-1 bg-white text-gray-700 rounded-full text-xs font-medium border border-gray-200 whitespace-nowrap">
                工商银行
            </button>
            <button class="px-3 py-1 bg-white text-gray-700 rounded-full text-xs font-medium border border-gray-200 whitespace-nowrap">
                建设银行
            </button>
            <button class="px-3 py-1 bg-white text-gray-700 rounded-full text-xs font-medium border border-gray-200 whitespace-nowrap">
                农业银行
            </button>
        </div>
    </div>

    <!-- 支行列表 -->
    <div class="px-6 pb-6 space-y-3">
        <% branches.forEach(function(branch, index) { %>
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <!-- 支行基本信息 -->
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                                <svg class="hero-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold text-black text-sm leading-tight">
                                    <%= branch.branch_name || '未知支行' %>
                                </h3>
                                <p class="text-gray-600 text-xs mt-1">
                                    <%= branch.bank ? branch.bank.bank_short_name : '未知银行' %>
                                </p>
                            </div>
                        </div>
                        
                        <!-- 联行号 -->
                        <div class="mb-2">
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 rounded-lg text-xs font-mono">
                                <%= branch.bank_no %>
                            </span>
                        </div>
                        
                        <!-- 地址信息 -->
                        <% if (branch.area) { %>
                            <div class="flex items-center space-x-1 mb-2">
                                <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                </svg>
                                <span class="text-gray-600 text-xs">
                                    <%= branch.area.province %><% if (branch.area.city && branch.area.city !== branch.area.province) { %> · <%= branch.area.city %><% } %>
                                </span>
                            </div>
                        <% } %>
                        
                        <!-- 详细地址 -->
                        <% if (branch.address) { %>
                            <p class="text-gray-500 text-xs leading-relaxed">
                                <%= branch.address %>
                            </p>
                        <% } %>
                        
                        <!-- 标签 -->
                        <div class="flex items-center space-x-2 mt-3">
                            <% if (branch.bank && branch.bank.bank_code) { %>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                                    <%= branch.bank.bank_code %>
                                </span>
                            <% } %>
                            <% if (branch.area && branch.area.area_code) { %>
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                                    <%= branch.area.area_code %>
                                </span>
                            <% } %>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex flex-col space-y-2 ml-3">
                        <button class="p-2 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                            </svg>
                        </button>
                        <button class="p-2 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </button>
                        <button class="p-2 bg-red-50 hover:bg-red-100 rounded-xl transition-colors">
                            <svg class="hero-icon text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        <% }); %>
    </div>

    <!-- 分页导航 -->
    <% if (pagination.pages > 1) { %>
        <div class="px-6 py-4 bg-white/50 border-t border-gray-100">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    第 <%= pagination.page %> 页，共 <%= pagination.pages %> 页
                </div>
                <div class="flex space-x-2">
                    <% if (pagination.page > 1) { %>
                        <a href="?page=<%= pagination.page - 1 %><%= search ? '&search=' + encodeURIComponent(search) : '' %>" 
                           class="px-3 py-2 bg-white rounded-lg border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                            上一页
                        </a>
                    <% } %>
                    <% if (pagination.page < pagination.pages) { %>
                        <a href="?page=<%= pagination.page + 1 %><%= search ? '&search=' + encodeURIComponent(search) : '' %>" 
                           class="px-3 py-2 bg-purple-500 rounded-lg text-white hover:bg-purple-600 transition-colors">
                            下一页
                        </a>
                    <% } %>
                </div>
            </div>
        </div>
    <% } %>

    <!-- 浮动添加按钮 -->
    <div class="fixed bottom-6 right-6">
        <button class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center">
            <svg class="hero-icon-lg text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
        </button>
    </div>

    <script>
        // 搜索功能
        const searchInput = document.querySelector('input[type="text"]');
        let searchTimeout;
        
        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = e.target.value.trim();
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    window.location.href = `/admin/branches?search=${encodeURIComponent(searchTerm)}`;
                }
            }, 500);
        });

        // 快速筛选
        document.querySelectorAll('button').forEach(btn => {
            if (btn.textContent.includes('银行') || btn.textContent.includes('全部')) {
                btn.addEventListener('click', function() {
                    const filterText = this.textContent.trim();
                    if (filterText !== '全部') {
                        window.location.href = `/admin/branches?search=${encodeURIComponent(filterText)}`;
                    } else {
                        window.location.href = '/admin/branches';
                    }
                });
            }
        });
    </script>
</body>
</html>
