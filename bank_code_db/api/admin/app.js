/**
 * 银行联行号查询系统 - 管理后台
 */

require('dotenv').config();

const express = require('express');
const path = require('path');
const session = require('express-session');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');

// 导入工具和配置
const database = require('../src/config/database');
const logger = require('../src/utils/logger');
const response = require('../src/utils/response');

// 创建 Express 应用
const app = express();

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// 会话配置
app.use(session({
  secret: process.env.ADMIN_SESSION_SECRET || 'admin-secret',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24小时
}));

// 文件上传配置
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('只支持 CSV 文件'));
    }
  }
});

// 认证中间件
const requireAuth = (req, res, next) => {
  if (req.session.admin) {
    next();
  } else {
    res.redirect('/admin/login');
  }
};

// 路由

// 登录页面
app.get('/admin/login', (req, res) => {
  res.render('login', { error: null });
});

// 登录处理
app.post('/admin/login', (req, res) => {
  const { username, password } = req.body;
  const adminUsername = process.env.ADMIN_USERNAME || 'admin';
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

  if (username === adminUsername && password === adminPassword) {
    req.session.admin = { username };
    res.redirect('/admin');
  } else {
    res.render('login', { error: '用户名或密码错误' });
  }
});

// 登出
app.get('/admin/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/admin/login');
});

// 管理后台首页
app.get('/admin', requireAuth, async (req, res) => {
  try {
    const db = database.getDb();

    // 获取统计信息
    const stats = {
      banks: await db.collection('banks').countDocuments() || 0,
      areas: await db.collection('areas').countDocuments() || 0,
      branches: await db.collection('bank_branches').countDocuments() || 0,
      queryLogs: 0 // 暂时设为0，因为query_logs集合可能不存在
    };

    // 获取最近的查询日志（如果集合存在）
    let recentLogs = [];
    try {
      const collections = await db.listCollections({ name: 'query_logs' }).toArray();
      if (collections.length > 0) {
        recentLogs = await db.collection('query_logs')
          .find({})
          .sort({ created_at: -1 })
          .limit(10)
          .toArray();
        stats.queryLogs = await db.collection('query_logs').countDocuments();
      }
    } catch (logError) {
      logger.warn('查询日志集合不存在或查询失败:', logError.message);
    }

    res.render('dashboard', {
      stats,
      recentLogs,
      admin: req.session.admin
    });
  } catch (error) {
    logger.error('获取管理后台数据失败:', error);
    res.render('error', { error: '获取数据失败: ' + error.message });
  }
});

// 银行管理
app.get('/admin/banks', requireAuth, async (req, res) => {
  try {
    const db = database.getDb();
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const skip = (page - 1) * limit;

    const [banks, total] = await Promise.all([
      db.collection('banks')
        .find({})
        .sort({ bank_code: 1 })
        .skip(skip)
        .limit(limit)
        .toArray(),
      db.collection('banks').countDocuments()
    ]);

    const pagination = {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    };

    res.render('banks', {
      banks,
      pagination,
      admin: req.session.admin
    });
  } catch (error) {
    logger.error('获取银行列表失败:', error);
    res.render('error', { error: '获取银行列表失败' });
  }
});

// 地区管理
app.get('/admin/areas', requireAuth, async (req, res) => {
  try {
    const db = database.getDb();
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const skip = (page - 1) * limit;

    const [areas, total] = await Promise.all([
      db.collection('areas')
        .find({})
        .sort({ area_code: 1 })
        .skip(skip)
        .limit(limit)
        .toArray(),
      db.collection('areas').countDocuments()
    ]);

    const pagination = {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    };

    res.render('areas', {
      areas,
      pagination,
      admin: req.session.admin
    });
  } catch (error) {
    logger.error('获取地区列表失败:', error);
    res.render('error', { error: '获取地区列表失败' });
  }
});

// 支行管理
app.get('/admin/branches', requireAuth, async (req, res) => {
  try {
    const db = database.getDb();
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const skip = (page - 1) * limit;
    const search = req.query.search || '';

    let filter = {};
    if (search) {
      filter = {
        $or: [
          { 'bank.bank_short_name': { $regex: search, $options: 'i' } },
          { 'branch_name': { $regex: search, $options: 'i' } },
          { 'bank_no': { $regex: search, $options: 'i' } },
          { 'address': { $regex: search, $options: 'i' } }
        ]
      };
    }

    const [branches, total] = await Promise.all([
      db.collection('bank_branches')
        .find(filter)
        .sort({ bank_no: 1 })
        .skip(skip)
        .limit(limit)
        .toArray(),
      db.collection('bank_branches').countDocuments(filter)
    ]);

    const pagination = {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    };

    res.render('branches', {
      branches,
      pagination,
      search,
      admin: req.session.admin
    });
  } catch (error) {
    logger.error('获取支行列表失败:', error);
    res.render('error', { error: '获取支行列表失败' });
  }
});

// 数据导入页面
app.get('/admin/import', requireAuth, (req, res) => {
  res.render('import', {
    admin: req.session.admin,
    message: null
  });
});

// 数据导入处理
app.post('/admin/import', requireAuth, upload.single('csvFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.render('import', {
        admin: req.session.admin,
        message: { type: 'error', text: '请选择要上传的 CSV 文件' }
      });
    }

    const results = [];
    const filePath = req.file.path;

    // 读取 CSV 文件
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        try {
          // 这里可以添加数据导入逻辑
          logger.info(`CSV 文件解析完成，共 ${results.length} 条记录`);

          // 清理临时文件
          fs.unlinkSync(filePath);

          res.render('import', {
            admin: req.session.admin,
            message: {
              type: 'success',
              text: `文件上传成功，共解析 ${results.length} 条记录`
            }
          });
        } catch (error) {
          logger.error('数据导入失败:', error);
          res.render('import', {
            admin: req.session.admin,
            message: { type: 'error', text: '数据导入失败' }
          });
        }
      });

  } catch (error) {
    logger.error('文件上传失败:', error);
    res.render('import', {
      admin: req.session.admin,
      message: { type: 'error', text: '文件上传失败' }
    });
  }
});

// 系统设置
app.get('/admin/settings', requireAuth, (req, res) => {
  res.render('settings', {
    admin: req.session.admin
  });
});

// 根路径重定向
app.get('/', (req, res) => {
  res.redirect('/admin');
});

// 启动服务器
const PORT = process.env.ADMIN_PORT || 3001;

async function startAdminServer() {
  try {
    // 连接数据库
    await database.connect();

    app.listen(PORT, () => {
      logger.info(`🔧 管理后台已启动: http://localhost:${PORT}/admin`);
      logger.info(`👤 默认账号: ${process.env.ADMIN_USERNAME || 'admin'}`);
      logger.info(`🔑 默认密码: ${process.env.ADMIN_PASSWORD || 'admin123'}`);
    });
  } catch (error) {
    logger.error('启动管理后台失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  startAdminServer();
}

module.exports = app;
