2025-05-27 00:01:57 [INFO]: 正在连接数据库...
2025-05-27 00:01:57 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:01:57 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:01:57 [INFO]: 数据库连接测试成功
2025-05-27 00:01:57 [INFO]: 🚀 银行联行号查询系统 API 服务已启动
2025-05-27 00:01:57 [INFO]: 📍 服务地址: http://localhost:3000
2025-05-27 00:01:57 [INFO]: 📖 API 文档: http://localhost:3000/docs
2025-05-27 00:01:57 [INFO]: 🏥 健康检查: http://localhost:3000/health
2025-05-27 00:01:57 [INFO]: 🌍 环境: development
2025-05-27 00:03:57 [INFO]: DB find on banks
2025-05-27 00:04:49 [INFO]: DB find on bank_branches
2025-05-27 00:05:40 [INFO]: DB aggregate on bank_branches
2025-05-27 00:05:55 [INFO]: DB aggregate on bank_branches
2025-05-27 00:07:37 [INFO]: 正在连接数据库...
2025-05-27 00:07:37 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:07:37 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:07:37 [INFO]: 数据库连接测试成功
2025-05-27 00:07:37 [INFO]: 🚀 银行联行号查询系统 API 服务已启动
2025-05-27 00:07:37 [INFO]: 📍 服务地址: http://localhost:3000
2025-05-27 00:07:37 [INFO]: 📖 API 文档: http://localhost:3000/docs
2025-05-27 00:07:37 [INFO]: 🏥 健康检查: http://localhost:3000/health
2025-05-27 00:07:37 [INFO]: 🌍 环境: development
2025-05-27 00:08:14 [INFO]: DB findOne on bank_branches
2025-05-27 00:08:41 [INFO]: DB find on bank_branches
2025-05-27 00:13:13 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:13:13 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:13:13 [INFO]: 数据库连接测试成功
2025-05-27 00:13:13 [INFO]: 🔧 管理后台已启动: http://localhost:3001/admin
2025-05-27 00:13:13 [INFO]: 👤 默认账号: admin
2025-05-27 00:13:13 [INFO]: 🔑 默认密码: admin123
2025-05-27 00:15:14 [INFO]: DB aggregate on bank_branches
2025-05-27 00:15:17 [WARN]: 404 - 路由未找到
