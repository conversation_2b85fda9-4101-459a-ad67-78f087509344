2025-05-27 00:01:57 [INFO]: 正在连接数据库...
2025-05-27 00:01:57 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:01:57 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:01:57 [INFO]: 数据库连接测试成功
2025-05-27 00:01:57 [INFO]: 🚀 银行联行号查询系统 API 服务已启动
2025-05-27 00:01:57 [INFO]: 📍 服务地址: http://localhost:3000
2025-05-27 00:01:57 [INFO]: 📖 API 文档: http://localhost:3000/docs
2025-05-27 00:01:57 [INFO]: 🏥 健康检查: http://localhost:3000/health
2025-05-27 00:01:57 [INFO]: 🌍 环境: development
2025-05-27 00:03:39 [HTTP]: 127.0.0.1 - - [26/May/2025:16:03:39 +0000] "GET /health HTTP/1.1" 200 216 "-" "curl/8.7.1"
2025-05-27 00:03:57 [INFO]: DB find on banks
2025-05-27 00:03:57 [HTTP]: GET /v1/banks?limit=3 200 160ms
2025-05-27 00:03:57 [HTTP]: 127.0.0.1 - - [26/May/2025:16:03:57 +0000] "GET /v1/banks?limit=3 HTTP/1.1" 200 1277 "-" "curl/8.7.1"
2025-05-27 00:04:49 [INFO]: DB find on bank_branches
2025-05-27 00:04:49 [HTTP]: GET /v1/branches?limit=2 200 1548ms
2025-05-27 00:04:49 [HTTP]: 127.0.0.1 - - [26/May/2025:16:04:49 +0000] "GET /v1/branches?limit=2 HTTP/1.1" 200 1949 "-" "curl/8.7.1"
2025-05-27 00:05:40 [INFO]: DB aggregate on bank_branches
2025-05-27 00:05:40 [HTTP]: GET /v1/branches/stats 200 565ms
2025-05-27 00:05:40 [HTTP]: 127.0.0.1 - - [26/May/2025:16:05:40 +0000] "GET /v1/branches/stats HTTP/1.1" 200 1082 "-" "curl/8.7.1"
2025-05-27 00:05:55 [INFO]: DB aggregate on bank_branches
2025-05-27 00:05:55 [HTTP]: GET /v1/branches/stats 200 457ms
2025-05-27 00:05:55 [HTTP]: 127.0.0.1 - - [26/May/2025:16:05:55 +0000] "GET /v1/branches/stats HTTP/1.1" 200 1082 "-" "curl/8.7.1"
2025-05-27 00:06:09 [HTTP]: 127.0.0.1 - - [26/May/2025:16:06:09 +0000] "GET /v1/branches/************ HTTP/1.1" 400 160 "-" "curl/8.7.1"
