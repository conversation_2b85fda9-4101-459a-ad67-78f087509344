2025-05-27 00:01:57 [INFO]: 正在连接数据库...
2025-05-27 00:01:57 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:01:57 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:01:57 [INFO]: 数据库连接测试成功
2025-05-27 00:01:57 [INFO]: 🚀 银行联行号查询系统 API 服务已启动
2025-05-27 00:01:57 [INFO]: 📍 服务地址: http://localhost:3000
2025-05-27 00:01:57 [INFO]: 📖 API 文档: http://localhost:3000/docs
2025-05-27 00:01:57 [INFO]: 🏥 健康检查: http://localhost:3000/health
2025-05-27 00:01:57 [INFO]: 🌍 环境: development
2025-05-27 00:03:39 [HTTP]: 127.0.0.1 - - [26/May/2025:16:03:39 +0000] "GET /health HTTP/1.1" 200 216 "-" "curl/8.7.1"
2025-05-27 00:03:57 [INFO]: DB find on banks
2025-05-27 00:03:57 [HTTP]: GET /v1/banks?limit=3 200 160ms
2025-05-27 00:03:57 [HTTP]: 127.0.0.1 - - [26/May/2025:16:03:57 +0000] "GET /v1/banks?limit=3 HTTP/1.1" 200 1277 "-" "curl/8.7.1"
2025-05-27 00:04:49 [INFO]: DB find on bank_branches
2025-05-27 00:04:49 [HTTP]: GET /v1/branches?limit=2 200 1548ms
2025-05-27 00:04:49 [HTTP]: 127.0.0.1 - - [26/May/2025:16:04:49 +0000] "GET /v1/branches?limit=2 HTTP/1.1" 200 1949 "-" "curl/8.7.1"
2025-05-27 00:05:40 [INFO]: DB aggregate on bank_branches
2025-05-27 00:05:40 [HTTP]: GET /v1/branches/stats 200 565ms
2025-05-27 00:05:40 [HTTP]: 127.0.0.1 - - [26/May/2025:16:05:40 +0000] "GET /v1/branches/stats HTTP/1.1" 200 1082 "-" "curl/8.7.1"
2025-05-27 00:05:55 [INFO]: DB aggregate on bank_branches
2025-05-27 00:05:55 [HTTP]: GET /v1/branches/stats 200 457ms
2025-05-27 00:05:55 [HTTP]: 127.0.0.1 - - [26/May/2025:16:05:55 +0000] "GET /v1/branches/stats HTTP/1.1" 200 1082 "-" "curl/8.7.1"
2025-05-27 00:06:09 [HTTP]: 127.0.0.1 - - [26/May/2025:16:06:09 +0000] "GET /v1/branches/************ HTTP/1.1" 400 160 "-" "curl/8.7.1"
2025-05-27 00:07:37 [INFO]: 正在连接数据库...
2025-05-27 00:07:37 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:07:37 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:07:37 [INFO]: 数据库连接测试成功
2025-05-27 00:07:37 [INFO]: 🚀 银行联行号查询系统 API 服务已启动
2025-05-27 00:07:37 [INFO]: 📍 服务地址: http://localhost:3000
2025-05-27 00:07:37 [INFO]: 📖 API 文档: http://localhost:3000/docs
2025-05-27 00:07:37 [INFO]: 🏥 健康检查: http://localhost:3000/health
2025-05-27 00:07:37 [INFO]: 🌍 环境: development
2025-05-27 00:08:14 [INFO]: DB findOne on bank_branches
2025-05-27 00:08:15 [HTTP]: 127.0.0.1 - - [26/May/2025:16:08:15 +0000] "GET /v1/branches/************ HTTP/1.1" 404 123 "-" "curl/8.7.1"
2025-05-27 00:08:41 [INFO]: DB find on bank_branches
2025-05-27 00:08:41 [HTTP]: GET /v1/branches?limit=1 200 1979ms
2025-05-27 00:08:41 [HTTP]: 127.0.0.1 - - [26/May/2025:16:08:41 +0000] "GET /v1/branches?limit=1 HTTP/1.1" 200 1090 "-" "curl/8.7.1"
2025-05-27 00:13:13 [INFO]: 正在连接到 MongoDB: mongodb://localhost:27017
2025-05-27 00:13:13 [INFO]: 成功连接到数据库: bank_code_db
2025-05-27 00:13:13 [INFO]: 数据库连接测试成功
2025-05-27 00:13:13 [INFO]: 🔧 管理后台已启动: http://localhost:3001/admin
2025-05-27 00:13:13 [INFO]: 👤 默认账号: admin
2025-05-27 00:13:13 [INFO]: 🔑 默认密码: admin123
2025-05-27 00:14:54 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:54 +0000] "GET /docs HTTP/1.1" 301 154 "http://localhost:3001/" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:14:54 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:54 +0000] "GET /docs/ HTTP/1.1" 200 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:14:55 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:55 +0000] "GET /docs/swagger-ui-init.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:14:55 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:55 +0000] "GET /docs/swagger-ui.css HTTP/1.1" 200 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:14:55 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:55 +0000] "GET /docs/swagger-ui-standalone-preset.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:14:55 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:55 +0000] "GET /docs/swagger-ui-bundle.js HTTP/1.1" 200 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:14:58 [HTTP]: 127.0.0.1 - - [26/May/2025:16:14:58 +0000] "GET /docs/favicon-32x32.png HTTP/1.1" 200 628 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:15:14 [INFO]: DB aggregate on bank_branches
2025-05-27 00:15:14 [HTTP]: GET /v1/branches/stats 200 1684ms
2025-05-27 00:15:14 [HTTP]: 127.0.0.1 - - [26/May/2025:16:15:14 +0000] "GET /v1/branches/stats HTTP/1.1" 200 - "http://localhost:3001/" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:15:17 [WARN]: 404 - 路由未找到
2025-05-27 00:15:17 [HTTP]: 127.0.0.1 - - [26/May/2025:16:15:17 +0000] "GET /favicon.ico HTTP/1.1" 404 109 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:23:38 [HTTP]: 127.0.0.1 - - [26/May/2025:16:23:38 +0000] "GET /docs/ HTTP/1.1" 304 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:23:38 [HTTP]: 127.0.0.1 - - [26/May/2025:16:23:38 +0000] "GET /docs/swagger-ui.css HTTP/1.1" 304 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:23:38 [HTTP]: 127.0.0.1 - - [26/May/2025:16:23:38 +0000] "GET /docs/swagger-ui-bundle.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:23:38 [HTTP]: 127.0.0.1 - - [26/May/2025:16:23:38 +0000] "GET /docs/swagger-ui-standalone-preset.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-27 00:23:39 [HTTP]: 127.0.0.1 - - [26/May/2025:16:23:39 +0000] "GET /docs/swagger-ui-init.js HTTP/1.1" 304 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
