/**
 * 银行支行路由
 */

const express = require('express');
const router = express.Router();

const branchController = require('../controllers/branchController');
const { validate } = require('../utils/validation');
const {
  branchQuerySchema,
  branchSearchSchema,
  batchQuerySchema,
  bankNoSchema
} = require('../utils/validation');
const { optionalApiKeyAuth } = require('../middleware/auth');
const { apiRateLimit, searchRateLimit, batchRateLimit } = require('../middleware/rateLimit');

/**
 * @swagger
 * components:
 *   schemas:
 *     Branch:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: 支行ID
 *         bank_no:
 *           type: string
 *           description: 联行号 (12位数字)
 *           example: "************"
 *         bank:
 *           type: object
 *           properties:
 *             bank_code:
 *               type: string
 *               description: 银行代码
 *               example: "102"
 *             bank_short_name:
 *               type: string
 *               description: 银行简称
 *               example: "工商银行"
 *             bank_full_name:
 *               type: string
 *               description: 银行全称
 *         area:
 *           type: object
 *           properties:
 *             area_code:
 *               type: string
 *               description: 地区代码
 *               example: "100000"
 *             province:
 *               type: string
 *               description: 省份
 *               example: "北京市"
 *             city:
 *               type: string
 *               description: 城市
 *               example: "北京市"
 *             district:
 *               type: string
 *               description: 区县
 *               example: "东城区"
 *         branch_name:
 *           type: string
 *           description: 支行名称
 *           example: "总行营业部"
 *         address:
 *           type: string
 *           description: 地址
 *         telephone:
 *           type: string
 *           description: 电话
 *         postal_code:
 *           type: string
 *           description: 邮政编码
 *     Pagination:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: 总记录数
 *         page:
 *           type: integer
 *           description: 当前页码
 *         limit:
 *           type: integer
 *           description: 每页数量
 *         pages:
 *           type: integer
 *           description: 总页数
 *         hasNext:
 *           type: boolean
 *           description: 是否有下一页
 *         hasPrev:
 *           type: boolean
 *           description: 是否有上一页
 */

/**
 * @swagger
 * /v1/branches:
 *   get:
 *     summary: 获取支行列表
 *     tags: [Branches]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: bank_code
 *         schema:
 *           type: string
 *           pattern: '^\\d{3}$'
 *         description: 银行代码
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: 省份
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: 城市
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 成功获取支行列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Branches retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Branch'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
router.get('/',
  optionalApiKeyAuth,
  apiRateLimit,
  validate(branchQuerySchema, 'query'),
  branchController.getBranches
);

/**
 * @swagger
 * /v1/branches/search:
 *   get:
 *     summary: 搜索支行
 *     tags: [Branches]
 *     parameters:
 *       - in: query
 *         name: keyword
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: 搜索关键词 (至少2个字符)
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, bank_name, branch_name, address, bank_no]
 *           default: all
 *         description: 搜索类型
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: 省份过滤
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: 城市过滤
 *       - in: query
 *         name: bank_code
 *         schema:
 *           type: string
 *           pattern: '^\\d{3}$'
 *         description: 银行代码过滤
 *       - in: query
 *         name: exact
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否精确匹配
 *     responses:
 *       200:
 *         description: 搜索结果
 *       400:
 *         description: 搜索关键词无效
 */
router.get('/search',
  optionalApiKeyAuth,
  searchRateLimit,
  validate(branchSearchSchema, 'query'),
  branchController.searchBranches
);

/**
 * @swagger
 * /v1/branches/batch:
 *   post:
 *     summary: 批量查询支行
 *     tags: [Branches]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bank_nos
 *             properties:
 *               bank_nos:
 *                 type: array
 *                 items:
 *                   type: string
 *                   pattern: '^\\d{12}$'
 *                 minItems: 1
 *                 maxItems: 100
 *                 description: 联行号数组 (最多100个)
 *                 example: ["************", "************"]
 *     responses:
 *       200:
 *         description: 批量查询结果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: 查询总数
 *                     found:
 *                       type: integer
 *                       description: 找到的数量
 *                     results:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           bank_no:
 *                             type: string
 *                           found:
 *                             type: boolean
 *                           data:
 *                             $ref: '#/components/schemas/Branch'
 *       400:
 *         description: 请求参数无效
 */
router.post('/batch',
  optionalApiKeyAuth,
  batchRateLimit,
  validate(batchQuerySchema, 'body'),
  branchController.getBranchesBatch
);

/**
 * @swagger
 * /v1/branches/stats:
 *   get:
 *     summary: 获取支行统计信息
 *     tags: [Branches]
 *     responses:
 *       200:
 *         description: 支行统计信息
 */
router.get('/stats',
  optionalApiKeyAuth,
  apiRateLimit,
  branchController.getBranchStats
);

/**
 * @swagger
 * /v1/branches/bank/{bankCode}:
 *   get:
 *     summary: 根据银行代码获取支行列表
 *     tags: [Branches]
 *     parameters:
 *       - in: path
 *         name: bankCode
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^\\d{3}$'
 *         description: 银行代码 (3位数字)
 *     responses:
 *       200:
 *         description: 支行列表
 */
router.get('/bank/:bankCode',
  optionalApiKeyAuth,
  apiRateLimit,
  branchController.getBranchesByBankCode
);

/**
 * @swagger
 * /v1/branches/area/{province}:
 *   get:
 *     summary: 根据省份获取支行列表
 *     tags: [Branches]
 *     parameters:
 *       - in: path
 *         name: province
 *         required: true
 *         schema:
 *           type: string
 *         description: 省份名称
 *     responses:
 *       200:
 *         description: 支行列表
 */
router.get('/area/:province',
  optionalApiKeyAuth,
  apiRateLimit,
  branchController.getBranchesByArea
);

/**
 * @swagger
 * /v1/branches/area/{province}/{city}:
 *   get:
 *     summary: 根据省份和城市获取支行列表
 *     tags: [Branches]
 */
router.get('/area/:province/:city',
  optionalApiKeyAuth,
  apiRateLimit,
  branchController.getBranchesByArea
);

/**
 * @swagger
 * /v1/branches/area/{province}/{city}/{district}:
 *   get:
 *     summary: 根据省份、城市和区县获取支行列表
 *     tags: [Branches]
 */
router.get('/area/:province/:city/:district',
  optionalApiKeyAuth,
  apiRateLimit,
  branchController.getBranchesByArea
);

/**
 * @swagger
 * /v1/branches/{bankNo}:
 *   get:
 *     summary: 根据联行号获取支行详情
 *     tags: [Branches]
 *     parameters:
 *       - in: path
 *         name: bankNo
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^\\d{12}$'
 *         description: 联行号 (12位数字)
 *         example: "************"
 *     responses:
 *       200:
 *         description: 支行详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Branch'
 *       400:
 *         description: 联行号格式无效
 *       404:
 *         description: 支行未找到
 */
router.get('/:bankNo',
  optionalApiKeyAuth,
  apiRateLimit,
  branchController.getBranchByBankNo
);

module.exports = router;
