#!/bin/bash

# 银行联行号查询系统 - 简化安装脚本
# 不需要 sudo 权限的版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
echo ""
echo "=================================================================="
echo "           银行联行号查询系统 - 简化安装"
echo "=================================================================="
echo ""

# 设置环境变量
log_info "设置 MongoDB 环境变量..."
export MONGODB_HOME=/usr/local/mongodb
export PATH=$MONGODB_HOME/bin:$PATH

# 检查 MongoDB
log_info "检查 MongoDB..."
if ! command -v mongod &> /dev/null; then
    log_error "MongoDB 不可用，请检查安装"
    exit 1
fi

MONGODB_VERSION=$(mongod --version | head -1)
log_success "MongoDB 版本: $MONGODB_VERSION"

# 创建数据目录（在用户目录下）
log_info "创建数据目录..."
mkdir -p ~/mongodb/data
mkdir -p ~/mongodb/logs
mkdir -p ~/mongodb/config

log_success "数据目录创建完成"

# 启动 MongoDB（使用命令行参数，避免配置文件问题）
log_info "启动 MongoDB..."
mongod --dbpath ~/mongodb/data --logpath ~/mongodb/logs/mongod.log --port 27017 --bind_ip 127.0.0.1 --fork

# 等待启动
sleep 3

# 检查是否启动成功
if pgrep mongod > /dev/null; then
    log_success "MongoDB 启动成功"
else
    log_error "MongoDB 启动失败，请检查日志: ~/mongodb/logs/mongod.log"
    exit 1
fi

# 测试连接（跳过，因为没有 mongo 客户端）
log_info "MongoDB 已启动，跳过连接测试（需要单独安装 MongoDB Shell）"

# 初始化数据库
log_info "初始化数据库..."
if [ -f "scripts/init_database.js" ]; then
    node scripts/init_database.js
    if [ $? -eq 0 ]; then
        log_success "数据库初始化完成"
    else
        log_error "数据库初始化失败"
        exit 1
    fi
else
    log_error "数据库初始化脚本不存在"
    exit 1
fi

# 检查数据文件
if [ -f "data/structured_bank_data_with_area.csv" ]; then
    log_info "导入银行联行号数据..."
    node scripts/import_data.js
    if [ $? -eq 0 ]; then
        log_success "数据导入完成"
    else
        log_error "数据导入失败"
        exit 1
    fi
else
    log_warning "数据文件 data/structured_bank_data_with_area.csv 不存在"
    log_info "请将数据文件放置在 data/ 目录下，然后运行: node scripts/import_data.js"
fi

# 验证系统
log_info "验证系统..."
if [ -f "scripts/verify_database.js" ]; then
    node scripts/verify_database.js
    if [ $? -eq 0 ]; then
        log_success "系统验证通过"
    else
        log_error "系统验证失败"
        exit 1
    fi
else
    log_error "系统验证脚本不存在"
    exit 1
fi

# 显示完成信息
echo ""
echo "=================================================================="
echo "                    🎉 安装完成！"
echo "=================================================================="
echo ""
echo "银行联行号查询系统已成功安装和配置。"
echo ""
echo "📋 系统信息:"
echo "- 数据库: bank_code_db"
echo "- MongoDB 端口: 27017"
echo "- 配置文件: ~/mongodb/config/mongod.conf"
echo "- 日志文件: ~/mongodb/logs/mongod.log"
echo "- 数据目录: ~/mongodb/data"
echo ""
echo "🚀 常用命令:"
echo "- 连接数据库: mongosh bank_code_db"
echo "- 查看日志: tail -f ~/mongodb/logs/mongod.log"
echo "- 停止 MongoDB: mongod --shutdown --config ~/mongodb/config/mongod.conf"
echo "- 验证系统: npm run verify"
echo ""
echo "🔧 下一步:"
echo "1. 开发 API 服务"
echo "2. 创建 Web 界面"
echo "3. 移动端适配"
echo ""
echo "=================================================================="
