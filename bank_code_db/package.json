{"name": "bank-code-db", "version": "1.0.0", "description": "银行联行号查询系统数据库管理工具", "main": "scripts/import_data.js", "scripts": {"init": "node scripts/init_database.js", "import": "node scripts/import_data.js", "create-indexes": "node scripts/create_indexes.js", "migrate": "node scripts/migrations.js", "verify": "node scripts/verify_database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mongodb", "bank", "database", "china", "banking"], "author": "Bank Code DB Team", "license": "MIT", "dependencies": {"commander": "^9.0.0", "csv-parser": "^3.0.0", "mongodb": "^5.0.0", "pinyin": "^3.0.0", "pinyin-pro": "^3.26.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/bank-code-db.git"}, "bugs": {"url": "https://github.com/your-org/bank-code-db/issues"}, "homepage": "https://github.com/your-org/bank-code-db#readme"}