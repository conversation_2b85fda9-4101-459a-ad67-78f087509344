import os
import sys
import datetime

def get_file_info(file_path):
    """
    获取文件的基本信息
    
    Args:
        file_path (str): 文件路径
    """
    try:
        # 获取文件统计信息
        stat_info = os.stat(file_path)
        
        # 获取文件大小
        size_bytes = stat_info.st_size
        size_kb = size_bytes / 1024
        size_mb = size_kb / 1024
        
        # 获取文件修改时间
        mod_time = datetime.datetime.fromtimestamp(stat_info.st_mtime)
        
        # 获取文件创建时间
        create_time = datetime.datetime.fromtimestamp(stat_info.st_ctime)
        
        # 打印文件信息
        print(f"\n{'='*50}")
        print(f"文件信息: {os.path.basename(file_path)}")
        print(f"{'='*50}")
        print(f"文件路径: {file_path}")
        print(f"文件大小: {size_bytes:,} 字节 ({size_mb:.2f} MB)")
        print(f"创建时间: {create_time}")
        print(f"修改时间: {mod_time}")
        print(f"文件扩展名: {os.path.splitext(file_path)[1]}")
        print(f"{'='*50}")
        
        # 如果是 CSV 或文本文件，尝试读取前几行
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.csv', '.txt']:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    lines = [line.strip() for line in f.readlines()[:10]]
                
                print("\n文件前10行内容:")
                for i, line in enumerate(lines):
                    print(f"第{i+1}行: {line[:100]}{'...' if len(line) > 100 else ''}")
                print(f"{'='*50}")
            except Exception as e:
                print(f"读取文件内容时出错: {str(e)}")
        
        # 根据文件名推测用途
        if "支行联行号" in file_path:
            print("""
文件用途:
这是一个银行支行联行号信息数据库，包含了银行支行的联行号和相关信息。

联行号（又称人行支付系统行号或超级网银行号）是由中国人民银行分配给各银行机构的唯一标识代码，用于银行间的转账结算和支付清算。

此文件的主要用途包括：
1. 银行间转账：当您需要跨行转账时，有些银行需要填写收款银行的联行号
2. 企业财务系统：企业在设置银行账户信息时可能需要填写联行号
3. 支付系统开发：开发支付相关系统时需要验证银行联行号
4. 银行信息查询：查询特定银行支行的详细信息和联行号

联行号通常是12位数字，前3位是银行代码，中间6位是地区代码，最后3位是支行代码。
""")
        
    except Exception as e:
        print(f"获取文件信息时出错: {str(e)}")

def main():
    dir_path = "/Users/<USER>/Downloads/hanghao"
    
    # 查找目标文件
    for file in os.listdir(dir_path):
        if "支行联行号" in file and file.endswith(".csv"):
            file_path = os.path.join(dir_path, file)
            get_file_info(file_path)
            return
    
    print(f"在 {dir_path} 中未找到包含'支行联行号'的 CSV 文件")

if __name__ == "__main__":
    main()
